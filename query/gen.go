// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                = new(Query)
	Chat             *chat
	ChatMessage      *chatMessage
	Device           *device
	DeviceType       *deviceType
	IntelligentAgent *intelligentAgent
	OTA              *oTA
	Post             *post
	PostCategory     *postCategory
	RoleCategory     *roleCategory
	RoleTemplate     *roleTemplate
	Setting          *setting
	Upload           *upload
	User             *user
	UserGroup        *userGroup
	Voiceprint       *voiceprint
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Chat = &Q.Chat
	ChatMessage = &Q.ChatMessage
	Device = &Q.Device
	DeviceType = &Q.DeviceType
	IntelligentAgent = &Q.IntelligentAgent
	OTA = &Q.OTA
	Post = &Q.Post
	PostCategory = &Q.PostCategory
	RoleCategory = &Q.RoleCategory
	RoleTemplate = &Q.RoleTemplate
	Setting = &Q.Setting
	Upload = &Q.Upload
	User = &Q.User
	UserGroup = &Q.UserGroup
	Voiceprint = &Q.Voiceprint
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:               db,
		Chat:             newChat(db, opts...),
		ChatMessage:      newChatMessage(db, opts...),
		Device:           newDevice(db, opts...),
		DeviceType:       newDeviceType(db, opts...),
		IntelligentAgent: newIntelligentAgent(db, opts...),
		OTA:              newOTA(db, opts...),
		Post:             newPost(db, opts...),
		PostCategory:     newPostCategory(db, opts...),
		RoleCategory:     newRoleCategory(db, opts...),
		RoleTemplate:     newRoleTemplate(db, opts...),
		Setting:          newSetting(db, opts...),
		Upload:           newUpload(db, opts...),
		User:             newUser(db, opts...),
		UserGroup:        newUserGroup(db, opts...),
		Voiceprint:       newVoiceprint(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Chat             chat
	ChatMessage      chatMessage
	Device           device
	DeviceType       deviceType
	IntelligentAgent intelligentAgent
	OTA              oTA
	Post             post
	PostCategory     postCategory
	RoleCategory     roleCategory
	RoleTemplate     roleTemplate
	Setting          setting
	Upload           upload
	User             user
	UserGroup        userGroup
	Voiceprint       voiceprint
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:               db,
		Chat:             q.Chat.clone(db),
		ChatMessage:      q.ChatMessage.clone(db),
		Device:           q.Device.clone(db),
		DeviceType:       q.DeviceType.clone(db),
		IntelligentAgent: q.IntelligentAgent.clone(db),
		OTA:              q.OTA.clone(db),
		Post:             q.Post.clone(db),
		PostCategory:     q.PostCategory.clone(db),
		RoleCategory:     q.RoleCategory.clone(db),
		RoleTemplate:     q.RoleTemplate.clone(db),
		Setting:          q.Setting.clone(db),
		Upload:           q.Upload.clone(db),
		User:             q.User.clone(db),
		UserGroup:        q.UserGroup.clone(db),
		Voiceprint:       q.Voiceprint.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:               db,
		Chat:             q.Chat.replaceDB(db),
		ChatMessage:      q.ChatMessage.replaceDB(db),
		Device:           q.Device.replaceDB(db),
		DeviceType:       q.DeviceType.replaceDB(db),
		IntelligentAgent: q.IntelligentAgent.replaceDB(db),
		OTA:              q.OTA.replaceDB(db),
		Post:             q.Post.replaceDB(db),
		PostCategory:     q.PostCategory.replaceDB(db),
		RoleCategory:     q.RoleCategory.replaceDB(db),
		RoleTemplate:     q.RoleTemplate.replaceDB(db),
		Setting:          q.Setting.replaceDB(db),
		Upload:           q.Upload.replaceDB(db),
		User:             q.User.replaceDB(db),
		UserGroup:        q.UserGroup.replaceDB(db),
		Voiceprint:       q.Voiceprint.replaceDB(db),
	}
}

type queryCtx struct {
	Chat             *chatDo
	ChatMessage      *chatMessageDo
	Device           *deviceDo
	DeviceType       *deviceTypeDo
	IntelligentAgent *intelligentAgentDo
	OTA              *oTADo
	Post             *postDo
	PostCategory     *postCategoryDo
	RoleCategory     *roleCategoryDo
	RoleTemplate     *roleTemplateDo
	Setting          *settingDo
	Upload           *uploadDo
	User             *userDo
	UserGroup        *userGroupDo
	Voiceprint       *voiceprintDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Chat:             q.Chat.WithContext(ctx),
		ChatMessage:      q.ChatMessage.WithContext(ctx),
		Device:           q.Device.WithContext(ctx),
		DeviceType:       q.DeviceType.WithContext(ctx),
		IntelligentAgent: q.IntelligentAgent.WithContext(ctx),
		OTA:              q.OTA.WithContext(ctx),
		Post:             q.Post.WithContext(ctx),
		PostCategory:     q.PostCategory.WithContext(ctx),
		RoleCategory:     q.RoleCategory.WithContext(ctx),
		RoleTemplate:     q.RoleTemplate.WithContext(ctx),
		Setting:          q.Setting.WithContext(ctx),
		Upload:           q.Upload.WithContext(ctx),
		User:             q.User.WithContext(ctx),
		UserGroup:        q.UserGroup.WithContext(ctx),
		Voiceprint:       q.Voiceprint.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
