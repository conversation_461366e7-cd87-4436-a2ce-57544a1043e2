// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newDeviceType(db *gorm.DB, opts ...gen.DOOption) deviceType {
	_deviceType := deviceType{}

	_deviceType.deviceTypeDo.UseDB(db, opts...)
	_deviceType.deviceTypeDo.UseModel(&model.DeviceType{})

	tableName := _deviceType.deviceTypeDo.TableName()
	_deviceType.ALL = field.NewAsterisk(tableName)
	_deviceType.ID = field.NewUint64(tableName, "id")
	_deviceType.CreatedAt = field.NewUint64(tableName, "created_at")
	_deviceType.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_deviceType.DeletedAt = field.NewUint(tableName, "deleted_at")
	_deviceType.Name = field.NewString(tableName, "name")
	_deviceType.Identifier = field.NewString(tableName, "identifier")
	_deviceType.DefaultRoleID = field.NewUint64(tableName, "default_role_id")
	_deviceType.DefaultRole = deviceTypeBelongsToDefaultRole{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DefaultRole", "model.RoleTemplate"),
		Img: struct {
			field.RelationField
			User struct {
				field.RelationField
				Avatar struct {
					field.RelationField
				}
				UserGroup struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("DefaultRole.Img", "model.Upload"),
			User: struct {
				field.RelationField
				Avatar struct {
					field.RelationField
				}
				UserGroup struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("DefaultRole.Img.User", "model.User"),
				Avatar: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("DefaultRole.Img.User.Avatar", "model.Upload"),
				},
				UserGroup: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("DefaultRole.Img.User.UserGroup", "model.UserGroup"),
				},
			},
		},
	}

	_deviceType.fillFieldMap()

	return _deviceType
}

type deviceType struct {
	deviceTypeDo

	ALL           field.Asterisk
	ID            field.Uint64
	CreatedAt     field.Uint64
	UpdatedAt     field.Uint64
	DeletedAt     field.Uint
	Name          field.String
	Identifier    field.String
	DefaultRoleID field.Uint64
	DefaultRole   deviceTypeBelongsToDefaultRole

	fieldMap map[string]field.Expr
}

func (d deviceType) Table(newTableName string) *deviceType {
	d.deviceTypeDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d deviceType) As(alias string) *deviceType {
	d.deviceTypeDo.DO = *(d.deviceTypeDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *deviceType) updateTableName(table string) *deviceType {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewUint64(table, "id")
	d.CreatedAt = field.NewUint64(table, "created_at")
	d.UpdatedAt = field.NewUint64(table, "updated_at")
	d.DeletedAt = field.NewUint(table, "deleted_at")
	d.Name = field.NewString(table, "name")
	d.Identifier = field.NewString(table, "identifier")
	d.DefaultRoleID = field.NewUint64(table, "default_role_id")

	d.fillFieldMap()

	return d
}

func (d *deviceType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *deviceType) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 8)
	d.fieldMap["id"] = d.ID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["name"] = d.Name
	d.fieldMap["identifier"] = d.Identifier
	d.fieldMap["default_role_id"] = d.DefaultRoleID

}

func (d deviceType) clone(db *gorm.DB) deviceType {
	d.deviceTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	d.DefaultRole.db = db.Session(&gorm.Session{Initialized: true})
	d.DefaultRole.db.Statement.ConnPool = db.Statement.ConnPool
	return d
}

func (d deviceType) replaceDB(db *gorm.DB) deviceType {
	d.deviceTypeDo.ReplaceDB(db)
	d.DefaultRole.db = db.Session(&gorm.Session{})
	return d
}

type deviceTypeBelongsToDefaultRole struct {
	db *gorm.DB

	field.RelationField

	Img struct {
		field.RelationField
		User struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}
	}
}

func (a deviceTypeBelongsToDefaultRole) Where(conds ...field.Expr) *deviceTypeBelongsToDefaultRole {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a deviceTypeBelongsToDefaultRole) WithContext(ctx context.Context) *deviceTypeBelongsToDefaultRole {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a deviceTypeBelongsToDefaultRole) Session(session *gorm.Session) *deviceTypeBelongsToDefaultRole {
	a.db = a.db.Session(session)
	return &a
}

func (a deviceTypeBelongsToDefaultRole) Model(m *model.DeviceType) *deviceTypeBelongsToDefaultRoleTx {
	return &deviceTypeBelongsToDefaultRoleTx{a.db.Model(m).Association(a.Name())}
}

func (a deviceTypeBelongsToDefaultRole) Unscoped() *deviceTypeBelongsToDefaultRole {
	a.db = a.db.Unscoped()
	return &a
}

type deviceTypeBelongsToDefaultRoleTx struct{ tx *gorm.Association }

func (a deviceTypeBelongsToDefaultRoleTx) Find() (result *model.RoleTemplate, err error) {
	return result, a.tx.Find(&result)
}

func (a deviceTypeBelongsToDefaultRoleTx) Append(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a deviceTypeBelongsToDefaultRoleTx) Replace(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a deviceTypeBelongsToDefaultRoleTx) Delete(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a deviceTypeBelongsToDefaultRoleTx) Clear() error {
	return a.tx.Clear()
}

func (a deviceTypeBelongsToDefaultRoleTx) Count() int64 {
	return a.tx.Count()
}

func (a deviceTypeBelongsToDefaultRoleTx) Unscoped() *deviceTypeBelongsToDefaultRoleTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type deviceTypeDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (d deviceTypeDo) FirstByID(id uint64) (result *model.DeviceType, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (d deviceTypeDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update device_types set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (d deviceTypeDo) Debug() *deviceTypeDo {
	return d.withDO(d.DO.Debug())
}

func (d deviceTypeDo) WithContext(ctx context.Context) *deviceTypeDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d deviceTypeDo) ReadDB() *deviceTypeDo {
	return d.Clauses(dbresolver.Read)
}

func (d deviceTypeDo) WriteDB() *deviceTypeDo {
	return d.Clauses(dbresolver.Write)
}

func (d deviceTypeDo) Session(config *gorm.Session) *deviceTypeDo {
	return d.withDO(d.DO.Session(config))
}

func (d deviceTypeDo) Clauses(conds ...clause.Expression) *deviceTypeDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d deviceTypeDo) Returning(value interface{}, columns ...string) *deviceTypeDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d deviceTypeDo) Not(conds ...gen.Condition) *deviceTypeDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d deviceTypeDo) Or(conds ...gen.Condition) *deviceTypeDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d deviceTypeDo) Select(conds ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d deviceTypeDo) Where(conds ...gen.Condition) *deviceTypeDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d deviceTypeDo) Order(conds ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d deviceTypeDo) Distinct(cols ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d deviceTypeDo) Omit(cols ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d deviceTypeDo) Join(table schema.Tabler, on ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d deviceTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d deviceTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d deviceTypeDo) Group(cols ...field.Expr) *deviceTypeDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d deviceTypeDo) Having(conds ...gen.Condition) *deviceTypeDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d deviceTypeDo) Limit(limit int) *deviceTypeDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d deviceTypeDo) Offset(offset int) *deviceTypeDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d deviceTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *deviceTypeDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d deviceTypeDo) Unscoped() *deviceTypeDo {
	return d.withDO(d.DO.Unscoped())
}

func (d deviceTypeDo) Create(values ...*model.DeviceType) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d deviceTypeDo) CreateInBatches(values []*model.DeviceType, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d deviceTypeDo) Save(values ...*model.DeviceType) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d deviceTypeDo) First() (*model.DeviceType, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DeviceType), nil
	}
}

func (d deviceTypeDo) Take() (*model.DeviceType, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DeviceType), nil
	}
}

func (d deviceTypeDo) Last() (*model.DeviceType, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DeviceType), nil
	}
}

func (d deviceTypeDo) Find() ([]*model.DeviceType, error) {
	result, err := d.DO.Find()
	return result.([]*model.DeviceType), err
}

func (d deviceTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DeviceType, err error) {
	buf := make([]*model.DeviceType, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d deviceTypeDo) FindInBatches(result *[]*model.DeviceType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d deviceTypeDo) Attrs(attrs ...field.AssignExpr) *deviceTypeDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d deviceTypeDo) Assign(attrs ...field.AssignExpr) *deviceTypeDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d deviceTypeDo) Joins(fields ...field.RelationField) *deviceTypeDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d deviceTypeDo) Preload(fields ...field.RelationField) *deviceTypeDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d deviceTypeDo) FirstOrInit() (*model.DeviceType, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DeviceType), nil
	}
}

func (d deviceTypeDo) FirstOrCreate() (*model.DeviceType, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DeviceType), nil
	}
}

func (d deviceTypeDo) FindByPage(offset int, limit int) (result []*model.DeviceType, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d deviceTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d deviceTypeDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d deviceTypeDo) Delete(models ...*model.DeviceType) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *deviceTypeDo) withDO(do gen.Dao) *deviceTypeDo {
	d.DO = *do.(*gen.DO)
	return d
}
