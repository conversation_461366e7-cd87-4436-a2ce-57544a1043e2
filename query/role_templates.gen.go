// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newRoleTemplate(db *gorm.DB, opts ...gen.DOOption) roleTemplate {
	_roleTemplate := roleTemplate{}

	_roleTemplate.roleTemplateDo.UseDB(db, opts...)
	_roleTemplate.roleTemplateDo.UseModel(&model.RoleTemplate{})

	tableName := _roleTemplate.roleTemplateDo.TableName()
	_roleTemplate.ALL = field.NewAsterisk(tableName)
	_roleTemplate.ID = field.NewUint64(tableName, "id")
	_roleTemplate.CreatedAt = field.NewUint64(tableName, "created_at")
	_roleTemplate.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_roleTemplate.DeletedAt = field.NewUint(tableName, "deleted_at")
	_roleTemplate.Name = field.NewString(tableName, "name")
	_roleTemplate.Description = field.NewString(tableName, "description")
	_roleTemplate.ImgID = field.NewUint64(tableName, "img_id")
	_roleTemplate.VoiceType = field.NewString(tableName, "voice_type")
	_roleTemplate.Prompt = field.NewString(tableName, "prompt")
	_roleTemplate.Language = field.NewString(tableName, "language")
	_roleTemplate.RoleCategoryIDs = field.NewField(tableName, "role_category_ids")
	_roleTemplate.Preset = field.NewBool(tableName, "preset")
	_roleTemplate.Img = roleTemplateBelongsToImg{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Img", "model.Upload"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Img.User", "model.User"),
			Avatar: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Img.User.Avatar", "model.Upload"),
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Img.User.UserGroup", "model.UserGroup"),
			},
		},
	}

	_roleTemplate.fillFieldMap()

	return _roleTemplate
}

type roleTemplate struct {
	roleTemplateDo

	ALL             field.Asterisk
	ID              field.Uint64
	CreatedAt       field.Uint64
	UpdatedAt       field.Uint64
	DeletedAt       field.Uint
	Name            field.String
	Description     field.String
	ImgID           field.Uint64
	VoiceType       field.String
	Prompt          field.String
	Language        field.String
	RoleCategoryIDs field.Field
	Preset          field.Bool
	Img             roleTemplateBelongsToImg

	fieldMap map[string]field.Expr
}

func (r roleTemplate) Table(newTableName string) *roleTemplate {
	r.roleTemplateDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r roleTemplate) As(alias string) *roleTemplate {
	r.roleTemplateDo.DO = *(r.roleTemplateDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *roleTemplate) updateTableName(table string) *roleTemplate {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewUint64(table, "id")
	r.CreatedAt = field.NewUint64(table, "created_at")
	r.UpdatedAt = field.NewUint64(table, "updated_at")
	r.DeletedAt = field.NewUint(table, "deleted_at")
	r.Name = field.NewString(table, "name")
	r.Description = field.NewString(table, "description")
	r.ImgID = field.NewUint64(table, "img_id")
	r.VoiceType = field.NewString(table, "voice_type")
	r.Prompt = field.NewString(table, "prompt")
	r.Language = field.NewString(table, "language")
	r.RoleCategoryIDs = field.NewField(table, "role_category_ids")
	r.Preset = field.NewBool(table, "preset")

	r.fillFieldMap()

	return r
}

func (r *roleTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *roleTemplate) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 13)
	r.fieldMap["id"] = r.ID
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["deleted_at"] = r.DeletedAt
	r.fieldMap["name"] = r.Name
	r.fieldMap["description"] = r.Description
	r.fieldMap["img_id"] = r.ImgID
	r.fieldMap["voice_type"] = r.VoiceType
	r.fieldMap["prompt"] = r.Prompt
	r.fieldMap["language"] = r.Language
	r.fieldMap["role_category_ids"] = r.RoleCategoryIDs
	r.fieldMap["preset"] = r.Preset

}

func (r roleTemplate) clone(db *gorm.DB) roleTemplate {
	r.roleTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	r.Img.db = db.Session(&gorm.Session{Initialized: true})
	r.Img.db.Statement.ConnPool = db.Statement.ConnPool
	return r
}

func (r roleTemplate) replaceDB(db *gorm.DB) roleTemplate {
	r.roleTemplateDo.ReplaceDB(db)
	r.Img.db = db.Session(&gorm.Session{})
	return r
}

type roleTemplateBelongsToImg struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
		}
		UserGroup struct {
			field.RelationField
		}
	}
}

func (a roleTemplateBelongsToImg) Where(conds ...field.Expr) *roleTemplateBelongsToImg {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a roleTemplateBelongsToImg) WithContext(ctx context.Context) *roleTemplateBelongsToImg {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a roleTemplateBelongsToImg) Session(session *gorm.Session) *roleTemplateBelongsToImg {
	a.db = a.db.Session(session)
	return &a
}

func (a roleTemplateBelongsToImg) Model(m *model.RoleTemplate) *roleTemplateBelongsToImgTx {
	return &roleTemplateBelongsToImgTx{a.db.Model(m).Association(a.Name())}
}

func (a roleTemplateBelongsToImg) Unscoped() *roleTemplateBelongsToImg {
	a.db = a.db.Unscoped()
	return &a
}

type roleTemplateBelongsToImgTx struct{ tx *gorm.Association }

func (a roleTemplateBelongsToImgTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a roleTemplateBelongsToImgTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a roleTemplateBelongsToImgTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a roleTemplateBelongsToImgTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a roleTemplateBelongsToImgTx) Clear() error {
	return a.tx.Clear()
}

func (a roleTemplateBelongsToImgTx) Count() int64 {
	return a.tx.Count()
}

func (a roleTemplateBelongsToImgTx) Unscoped() *roleTemplateBelongsToImgTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type roleTemplateDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (r roleTemplateDo) FirstByID(id uint64) (result *model.RoleTemplate, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (r roleTemplateDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update role_templates set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (r roleTemplateDo) Debug() *roleTemplateDo {
	return r.withDO(r.DO.Debug())
}

func (r roleTemplateDo) WithContext(ctx context.Context) *roleTemplateDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r roleTemplateDo) ReadDB() *roleTemplateDo {
	return r.Clauses(dbresolver.Read)
}

func (r roleTemplateDo) WriteDB() *roleTemplateDo {
	return r.Clauses(dbresolver.Write)
}

func (r roleTemplateDo) Session(config *gorm.Session) *roleTemplateDo {
	return r.withDO(r.DO.Session(config))
}

func (r roleTemplateDo) Clauses(conds ...clause.Expression) *roleTemplateDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r roleTemplateDo) Returning(value interface{}, columns ...string) *roleTemplateDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r roleTemplateDo) Not(conds ...gen.Condition) *roleTemplateDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r roleTemplateDo) Or(conds ...gen.Condition) *roleTemplateDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r roleTemplateDo) Select(conds ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r roleTemplateDo) Where(conds ...gen.Condition) *roleTemplateDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r roleTemplateDo) Order(conds ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r roleTemplateDo) Distinct(cols ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r roleTemplateDo) Omit(cols ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r roleTemplateDo) Join(table schema.Tabler, on ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r roleTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r roleTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r roleTemplateDo) Group(cols ...field.Expr) *roleTemplateDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r roleTemplateDo) Having(conds ...gen.Condition) *roleTemplateDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r roleTemplateDo) Limit(limit int) *roleTemplateDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r roleTemplateDo) Offset(offset int) *roleTemplateDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r roleTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *roleTemplateDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r roleTemplateDo) Unscoped() *roleTemplateDo {
	return r.withDO(r.DO.Unscoped())
}

func (r roleTemplateDo) Create(values ...*model.RoleTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r roleTemplateDo) CreateInBatches(values []*model.RoleTemplate, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r roleTemplateDo) Save(values ...*model.RoleTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r roleTemplateDo) First() (*model.RoleTemplate, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleTemplate), nil
	}
}

func (r roleTemplateDo) Take() (*model.RoleTemplate, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleTemplate), nil
	}
}

func (r roleTemplateDo) Last() (*model.RoleTemplate, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleTemplate), nil
	}
}

func (r roleTemplateDo) Find() ([]*model.RoleTemplate, error) {
	result, err := r.DO.Find()
	return result.([]*model.RoleTemplate), err
}

func (r roleTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.RoleTemplate, err error) {
	buf := make([]*model.RoleTemplate, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r roleTemplateDo) FindInBatches(result *[]*model.RoleTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r roleTemplateDo) Attrs(attrs ...field.AssignExpr) *roleTemplateDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r roleTemplateDo) Assign(attrs ...field.AssignExpr) *roleTemplateDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r roleTemplateDo) Joins(fields ...field.RelationField) *roleTemplateDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r roleTemplateDo) Preload(fields ...field.RelationField) *roleTemplateDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r roleTemplateDo) FirstOrInit() (*model.RoleTemplate, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleTemplate), nil
	}
}

func (r roleTemplateDo) FirstOrCreate() (*model.RoleTemplate, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleTemplate), nil
	}
}

func (r roleTemplateDo) FindByPage(offset int, limit int) (result []*model.RoleTemplate, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r roleTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r roleTemplateDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r roleTemplateDo) Delete(models ...*model.RoleTemplate) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *roleTemplateDo) withDO(do gen.Dao) *roleTemplateDo {
	r.DO = *do.(*gen.DO)
	return r
}
