// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newVoiceprint(db *gorm.DB, opts ...gen.DOOption) voiceprint {
	_voiceprint := voiceprint{}

	_voiceprint.voiceprintDo.UseDB(db, opts...)
	_voiceprint.voiceprintDo.UseModel(&model.Voiceprint{})

	tableName := _voiceprint.voiceprintDo.TableName()
	_voiceprint.ALL = field.NewAsterisk(tableName)
	_voiceprint.ID = field.NewUint64(tableName, "id")
	_voiceprint.CreatedAt = field.NewUint64(tableName, "created_at")
	_voiceprint.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_voiceprint.DeletedAt = field.NewUint(tableName, "deleted_at")
	_voiceprint.Name = field.NewString(tableName, "name")
	_voiceprint.Embedding = field.NewString(tableName, "embedding")
	_voiceprint.Salutation = field.NewString(tableName, "salutation")
	_voiceprint.Description = field.NewString(tableName, "description")
	_voiceprint.UserID = field.NewUint64(tableName, "user_id")
	_voiceprint.ImgID = field.NewUint64(tableName, "img_id")
	_voiceprint.User = voiceprintBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.UserGroup", "model.UserGroup"),
		},
	}

	_voiceprint.Img = voiceprintBelongsToImg{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Img", "model.Upload"),
	}

	_voiceprint.fillFieldMap()

	return _voiceprint
}

type voiceprint struct {
	voiceprintDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Name        field.String
	Embedding   field.String
	Salutation  field.String
	Description field.String
	UserID      field.Uint64
	ImgID       field.Uint64
	User        voiceprintBelongsToUser

	Img voiceprintBelongsToImg

	fieldMap map[string]field.Expr
}

func (v voiceprint) Table(newTableName string) *voiceprint {
	v.voiceprintDo.UseTable(newTableName)
	return v.updateTableName(newTableName)
}

func (v voiceprint) As(alias string) *voiceprint {
	v.voiceprintDo.DO = *(v.voiceprintDo.As(alias).(*gen.DO))
	return v.updateTableName(alias)
}

func (v *voiceprint) updateTableName(table string) *voiceprint {
	v.ALL = field.NewAsterisk(table)
	v.ID = field.NewUint64(table, "id")
	v.CreatedAt = field.NewUint64(table, "created_at")
	v.UpdatedAt = field.NewUint64(table, "updated_at")
	v.DeletedAt = field.NewUint(table, "deleted_at")
	v.Name = field.NewString(table, "name")
	v.Embedding = field.NewString(table, "embedding")
	v.Salutation = field.NewString(table, "salutation")
	v.Description = field.NewString(table, "description")
	v.UserID = field.NewUint64(table, "user_id")
	v.ImgID = field.NewUint64(table, "img_id")

	v.fillFieldMap()

	return v
}

func (v *voiceprint) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := v.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (v *voiceprint) fillFieldMap() {
	v.fieldMap = make(map[string]field.Expr, 12)
	v.fieldMap["id"] = v.ID
	v.fieldMap["created_at"] = v.CreatedAt
	v.fieldMap["updated_at"] = v.UpdatedAt
	v.fieldMap["deleted_at"] = v.DeletedAt
	v.fieldMap["name"] = v.Name
	v.fieldMap["embedding"] = v.Embedding
	v.fieldMap["salutation"] = v.Salutation
	v.fieldMap["description"] = v.Description
	v.fieldMap["user_id"] = v.UserID
	v.fieldMap["img_id"] = v.ImgID

}

func (v voiceprint) clone(db *gorm.DB) voiceprint {
	v.voiceprintDo.ReplaceConnPool(db.Statement.ConnPool)
	v.User.db = db.Session(&gorm.Session{Initialized: true})
	v.User.db.Statement.ConnPool = db.Statement.ConnPool
	v.Img.db = db.Session(&gorm.Session{Initialized: true})
	v.Img.db.Statement.ConnPool = db.Statement.ConnPool
	return v
}

func (v voiceprint) replaceDB(db *gorm.DB) voiceprint {
	v.voiceprintDo.ReplaceDB(db)
	v.User.db = db.Session(&gorm.Session{})
	v.Img.db = db.Session(&gorm.Session{})
	return v
}

type voiceprintBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a voiceprintBelongsToUser) Where(conds ...field.Expr) *voiceprintBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a voiceprintBelongsToUser) WithContext(ctx context.Context) *voiceprintBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a voiceprintBelongsToUser) Session(session *gorm.Session) *voiceprintBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a voiceprintBelongsToUser) Model(m *model.Voiceprint) *voiceprintBelongsToUserTx {
	return &voiceprintBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a voiceprintBelongsToUser) Unscoped() *voiceprintBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type voiceprintBelongsToUserTx struct{ tx *gorm.Association }

func (a voiceprintBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a voiceprintBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a voiceprintBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a voiceprintBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a voiceprintBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a voiceprintBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a voiceprintBelongsToUserTx) Unscoped() *voiceprintBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type voiceprintBelongsToImg struct {
	db *gorm.DB

	field.RelationField
}

func (a voiceprintBelongsToImg) Where(conds ...field.Expr) *voiceprintBelongsToImg {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a voiceprintBelongsToImg) WithContext(ctx context.Context) *voiceprintBelongsToImg {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a voiceprintBelongsToImg) Session(session *gorm.Session) *voiceprintBelongsToImg {
	a.db = a.db.Session(session)
	return &a
}

func (a voiceprintBelongsToImg) Model(m *model.Voiceprint) *voiceprintBelongsToImgTx {
	return &voiceprintBelongsToImgTx{a.db.Model(m).Association(a.Name())}
}

func (a voiceprintBelongsToImg) Unscoped() *voiceprintBelongsToImg {
	a.db = a.db.Unscoped()
	return &a
}

type voiceprintBelongsToImgTx struct{ tx *gorm.Association }

func (a voiceprintBelongsToImgTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a voiceprintBelongsToImgTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a voiceprintBelongsToImgTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a voiceprintBelongsToImgTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a voiceprintBelongsToImgTx) Clear() error {
	return a.tx.Clear()
}

func (a voiceprintBelongsToImgTx) Count() int64 {
	return a.tx.Count()
}

func (a voiceprintBelongsToImgTx) Unscoped() *voiceprintBelongsToImgTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type voiceprintDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (v voiceprintDo) FirstByID(id uint64) (result *model.Voiceprint, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (v voiceprintDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update voiceprints set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (v voiceprintDo) Debug() *voiceprintDo {
	return v.withDO(v.DO.Debug())
}

func (v voiceprintDo) WithContext(ctx context.Context) *voiceprintDo {
	return v.withDO(v.DO.WithContext(ctx))
}

func (v voiceprintDo) ReadDB() *voiceprintDo {
	return v.Clauses(dbresolver.Read)
}

func (v voiceprintDo) WriteDB() *voiceprintDo {
	return v.Clauses(dbresolver.Write)
}

func (v voiceprintDo) Session(config *gorm.Session) *voiceprintDo {
	return v.withDO(v.DO.Session(config))
}

func (v voiceprintDo) Clauses(conds ...clause.Expression) *voiceprintDo {
	return v.withDO(v.DO.Clauses(conds...))
}

func (v voiceprintDo) Returning(value interface{}, columns ...string) *voiceprintDo {
	return v.withDO(v.DO.Returning(value, columns...))
}

func (v voiceprintDo) Not(conds ...gen.Condition) *voiceprintDo {
	return v.withDO(v.DO.Not(conds...))
}

func (v voiceprintDo) Or(conds ...gen.Condition) *voiceprintDo {
	return v.withDO(v.DO.Or(conds...))
}

func (v voiceprintDo) Select(conds ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Select(conds...))
}

func (v voiceprintDo) Where(conds ...gen.Condition) *voiceprintDo {
	return v.withDO(v.DO.Where(conds...))
}

func (v voiceprintDo) Order(conds ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Order(conds...))
}

func (v voiceprintDo) Distinct(cols ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Distinct(cols...))
}

func (v voiceprintDo) Omit(cols ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Omit(cols...))
}

func (v voiceprintDo) Join(table schema.Tabler, on ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Join(table, on...))
}

func (v voiceprintDo) LeftJoin(table schema.Tabler, on ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.LeftJoin(table, on...))
}

func (v voiceprintDo) RightJoin(table schema.Tabler, on ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.RightJoin(table, on...))
}

func (v voiceprintDo) Group(cols ...field.Expr) *voiceprintDo {
	return v.withDO(v.DO.Group(cols...))
}

func (v voiceprintDo) Having(conds ...gen.Condition) *voiceprintDo {
	return v.withDO(v.DO.Having(conds...))
}

func (v voiceprintDo) Limit(limit int) *voiceprintDo {
	return v.withDO(v.DO.Limit(limit))
}

func (v voiceprintDo) Offset(offset int) *voiceprintDo {
	return v.withDO(v.DO.Offset(offset))
}

func (v voiceprintDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *voiceprintDo {
	return v.withDO(v.DO.Scopes(funcs...))
}

func (v voiceprintDo) Unscoped() *voiceprintDo {
	return v.withDO(v.DO.Unscoped())
}

func (v voiceprintDo) Create(values ...*model.Voiceprint) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Create(values)
}

func (v voiceprintDo) CreateInBatches(values []*model.Voiceprint, batchSize int) error {
	return v.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (v voiceprintDo) Save(values ...*model.Voiceprint) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Save(values)
}

func (v voiceprintDo) First() (*model.Voiceprint, error) {
	if result, err := v.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Voiceprint), nil
	}
}

func (v voiceprintDo) Take() (*model.Voiceprint, error) {
	if result, err := v.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Voiceprint), nil
	}
}

func (v voiceprintDo) Last() (*model.Voiceprint, error) {
	if result, err := v.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Voiceprint), nil
	}
}

func (v voiceprintDo) Find() ([]*model.Voiceprint, error) {
	result, err := v.DO.Find()
	return result.([]*model.Voiceprint), err
}

func (v voiceprintDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Voiceprint, err error) {
	buf := make([]*model.Voiceprint, 0, batchSize)
	err = v.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (v voiceprintDo) FindInBatches(result *[]*model.Voiceprint, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return v.DO.FindInBatches(result, batchSize, fc)
}

func (v voiceprintDo) Attrs(attrs ...field.AssignExpr) *voiceprintDo {
	return v.withDO(v.DO.Attrs(attrs...))
}

func (v voiceprintDo) Assign(attrs ...field.AssignExpr) *voiceprintDo {
	return v.withDO(v.DO.Assign(attrs...))
}

func (v voiceprintDo) Joins(fields ...field.RelationField) *voiceprintDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Joins(_f))
	}
	return &v
}

func (v voiceprintDo) Preload(fields ...field.RelationField) *voiceprintDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Preload(_f))
	}
	return &v
}

func (v voiceprintDo) FirstOrInit() (*model.Voiceprint, error) {
	if result, err := v.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Voiceprint), nil
	}
}

func (v voiceprintDo) FirstOrCreate() (*model.Voiceprint, error) {
	if result, err := v.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Voiceprint), nil
	}
}

func (v voiceprintDo) FindByPage(offset int, limit int) (result []*model.Voiceprint, count int64, err error) {
	result, err = v.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = v.Offset(-1).Limit(-1).Count()
	return
}

func (v voiceprintDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = v.Count()
	if err != nil {
		return
	}

	err = v.Offset(offset).Limit(limit).Scan(result)
	return
}

func (v voiceprintDo) Scan(result interface{}) (err error) {
	return v.DO.Scan(result)
}

func (v voiceprintDo) Delete(models ...*model.Voiceprint) (result gen.ResultInfo, err error) {
	return v.DO.Delete(models)
}

func (v *voiceprintDo) withDO(do gen.Dao) *voiceprintDo {
	v.DO = *do.(*gen.DO)
	return v
}
