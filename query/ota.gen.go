// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newOTA(db *gorm.DB, opts ...gen.DOOption) oTA {
	_oTA := oTA{}

	_oTA.oTADo.UseDB(db, opts...)
	_oTA.oTADo.UseModel(&model.OTA{})

	tableName := _oTA.oTADo.TableName()
	_oTA.ALL = field.NewAsterisk(tableName)
	_oTA.ID = field.NewUint64(tableName, "id")
	_oTA.CreatedAt = field.NewUint64(tableName, "created_at")
	_oTA.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_oTA.DeletedAt = field.NewUint(tableName, "deleted_at")
	_oTA.Version = field.NewString(tableName, "version")
	_oTA.URL = field.NewString(tableName, "url")
	_oTA.ReleaseNote = field.NewString(tableName, "release_note")
	_oTA.Channel = field.NewInt(tableName, "channel")

	_oTA.fillFieldMap()

	return _oTA
}

type oTA struct {
	oTADo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Version     field.String
	URL         field.String
	ReleaseNote field.String
	Channel     field.Int

	fieldMap map[string]field.Expr
}

func (o oTA) Table(newTableName string) *oTA {
	o.oTADo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o oTA) As(alias string) *oTA {
	o.oTADo.DO = *(o.oTADo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *oTA) updateTableName(table string) *oTA {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewUint64(table, "id")
	o.CreatedAt = field.NewUint64(table, "created_at")
	o.UpdatedAt = field.NewUint64(table, "updated_at")
	o.DeletedAt = field.NewUint(table, "deleted_at")
	o.Version = field.NewString(table, "version")
	o.URL = field.NewString(table, "url")
	o.ReleaseNote = field.NewString(table, "release_note")
	o.Channel = field.NewInt(table, "channel")

	o.fillFieldMap()

	return o
}

func (o *oTA) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *oTA) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 8)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
	o.fieldMap["version"] = o.Version
	o.fieldMap["url"] = o.URL
	o.fieldMap["release_note"] = o.ReleaseNote
	o.fieldMap["channel"] = o.Channel
}

func (o oTA) clone(db *gorm.DB) oTA {
	o.oTADo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o oTA) replaceDB(db *gorm.DB) oTA {
	o.oTADo.ReplaceDB(db)
	return o
}

type oTADo struct{ gen.DO }

// FirstByID Where("id=@id")
func (o oTADo) FirstByID(id uint64) (result *model.OTA, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = o.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (o oTADo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update ota set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = o.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (o oTADo) Debug() *oTADo {
	return o.withDO(o.DO.Debug())
}

func (o oTADo) WithContext(ctx context.Context) *oTADo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o oTADo) ReadDB() *oTADo {
	return o.Clauses(dbresolver.Read)
}

func (o oTADo) WriteDB() *oTADo {
	return o.Clauses(dbresolver.Write)
}

func (o oTADo) Session(config *gorm.Session) *oTADo {
	return o.withDO(o.DO.Session(config))
}

func (o oTADo) Clauses(conds ...clause.Expression) *oTADo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o oTADo) Returning(value interface{}, columns ...string) *oTADo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o oTADo) Not(conds ...gen.Condition) *oTADo {
	return o.withDO(o.DO.Not(conds...))
}

func (o oTADo) Or(conds ...gen.Condition) *oTADo {
	return o.withDO(o.DO.Or(conds...))
}

func (o oTADo) Select(conds ...field.Expr) *oTADo {
	return o.withDO(o.DO.Select(conds...))
}

func (o oTADo) Where(conds ...gen.Condition) *oTADo {
	return o.withDO(o.DO.Where(conds...))
}

func (o oTADo) Order(conds ...field.Expr) *oTADo {
	return o.withDO(o.DO.Order(conds...))
}

func (o oTADo) Distinct(cols ...field.Expr) *oTADo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o oTADo) Omit(cols ...field.Expr) *oTADo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o oTADo) Join(table schema.Tabler, on ...field.Expr) *oTADo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o oTADo) LeftJoin(table schema.Tabler, on ...field.Expr) *oTADo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o oTADo) RightJoin(table schema.Tabler, on ...field.Expr) *oTADo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o oTADo) Group(cols ...field.Expr) *oTADo {
	return o.withDO(o.DO.Group(cols...))
}

func (o oTADo) Having(conds ...gen.Condition) *oTADo {
	return o.withDO(o.DO.Having(conds...))
}

func (o oTADo) Limit(limit int) *oTADo {
	return o.withDO(o.DO.Limit(limit))
}

func (o oTADo) Offset(offset int) *oTADo {
	return o.withDO(o.DO.Offset(offset))
}

func (o oTADo) Scopes(funcs ...func(gen.Dao) gen.Dao) *oTADo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o oTADo) Unscoped() *oTADo {
	return o.withDO(o.DO.Unscoped())
}

func (o oTADo) Create(values ...*model.OTA) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o oTADo) CreateInBatches(values []*model.OTA, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o oTADo) Save(values ...*model.OTA) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o oTADo) First() (*model.OTA, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OTA), nil
	}
}

func (o oTADo) Take() (*model.OTA, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OTA), nil
	}
}

func (o oTADo) Last() (*model.OTA, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OTA), nil
	}
}

func (o oTADo) Find() ([]*model.OTA, error) {
	result, err := o.DO.Find()
	return result.([]*model.OTA), err
}

func (o oTADo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OTA, err error) {
	buf := make([]*model.OTA, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o oTADo) FindInBatches(result *[]*model.OTA, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o oTADo) Attrs(attrs ...field.AssignExpr) *oTADo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o oTADo) Assign(attrs ...field.AssignExpr) *oTADo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o oTADo) Joins(fields ...field.RelationField) *oTADo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o oTADo) Preload(fields ...field.RelationField) *oTADo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o oTADo) FirstOrInit() (*model.OTA, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OTA), nil
	}
}

func (o oTADo) FirstOrCreate() (*model.OTA, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OTA), nil
	}
}

func (o oTADo) FindByPage(offset int, limit int) (result []*model.OTA, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o oTADo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o oTADo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o oTADo) Delete(models ...*model.OTA) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *oTADo) withDO(do gen.Dao) *oTADo {
	o.DO = *do.(*gen.DO)
	return o
}
