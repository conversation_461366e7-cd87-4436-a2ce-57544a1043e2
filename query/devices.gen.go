// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newDevice(db *gorm.DB, opts ...gen.DOOption) device {
	_device := device{}

	_device.deviceDo.UseDB(db, opts...)
	_device.deviceDo.UseModel(&model.Device{})

	tableName := _device.deviceDo.TableName()
	_device.ALL = field.NewAsterisk(tableName)
	_device.ID = field.NewUint64(tableName, "id")
	_device.CreatedAt = field.NewUint64(tableName, "created_at")
	_device.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_device.DeletedAt = field.NewUint(tableName, "deleted_at")
	_device.MAC = field.NewString(tableName, "mac")
	_device.Remark = field.NewString(tableName, "remark")
	_device.UserId = field.NewUint64(tableName, "user_id")
	_device.IntelligentAgentID = field.NewUint64(tableName, "intelligent_agent_id")
	_device.OTAEnabled = field.NewBool(tableName, "ota_enabled")
	_device.OTAChannel = field.NewInt(tableName, "ota_channel")
	_device.Code = field.NewString(tableName, "code")
	_device.Identifier = field.NewString(tableName, "identifier")
	_device.SystemVersion = field.NewString(tableName, "system_version")
	_device.DeviceType = deviceHasOneDeviceType{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DeviceType", "model.DeviceType"),
		DefaultRole: struct {
			field.RelationField
			Img struct {
				field.RelationField
				User struct {
					field.RelationField
					Avatar struct {
						field.RelationField
					}
					UserGroup struct {
						field.RelationField
					}
				}
			}
		}{
			RelationField: field.NewRelation("DeviceType.DefaultRole", "model.RoleTemplate"),
			Img: struct {
				field.RelationField
				User struct {
					field.RelationField
					Avatar struct {
						field.RelationField
					}
					UserGroup struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("DeviceType.DefaultRole.Img", "model.Upload"),
				User: struct {
					field.RelationField
					Avatar struct {
						field.RelationField
					}
					UserGroup struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("DeviceType.DefaultRole.Img.User", "model.User"),
					Avatar: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("DeviceType.DefaultRole.Img.User.Avatar", "model.Upload"),
					},
					UserGroup: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("DeviceType.DefaultRole.Img.User.UserGroup", "model.UserGroup"),
					},
				},
			},
		},
	}

	_device.User = deviceBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_device.IntelligentAgent = deviceBelongsToIntelligentAgent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("IntelligentAgent", "model.IntelligentAgent"),
		User: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("IntelligentAgent.User", "model.User"),
		},
		Img: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("IntelligentAgent.Img", "model.Upload"),
		},
		RoleTemplate: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("IntelligentAgent.RoleTemplate", "model.RoleTemplate"),
		},
		Devices: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			IntelligentAgent struct {
				field.RelationField
			}
			DeviceType struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("IntelligentAgent.Devices", "model.Device"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("IntelligentAgent.Devices.User", "model.User"),
			},
			IntelligentAgent: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("IntelligentAgent.Devices.IntelligentAgent", "model.IntelligentAgent"),
			},
			DeviceType: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("IntelligentAgent.Devices.DeviceType", "model.DeviceType"),
			},
		},
	}

	_device.fillFieldMap()

	return _device
}

type device struct {
	deviceDo

	ALL                field.Asterisk
	ID                 field.Uint64
	CreatedAt          field.Uint64
	UpdatedAt          field.Uint64
	DeletedAt          field.Uint
	MAC                field.String
	Remark             field.String
	UserId             field.Uint64
	IntelligentAgentID field.Uint64
	OTAEnabled         field.Bool
	OTAChannel         field.Int
	Code               field.String
	Identifier         field.String
	SystemVersion      field.String
	DeviceType         deviceHasOneDeviceType

	User deviceBelongsToUser

	IntelligentAgent deviceBelongsToIntelligentAgent

	fieldMap map[string]field.Expr
}

func (d device) Table(newTableName string) *device {
	d.deviceDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d device) As(alias string) *device {
	d.deviceDo.DO = *(d.deviceDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *device) updateTableName(table string) *device {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewUint64(table, "id")
	d.CreatedAt = field.NewUint64(table, "created_at")
	d.UpdatedAt = field.NewUint64(table, "updated_at")
	d.DeletedAt = field.NewUint(table, "deleted_at")
	d.MAC = field.NewString(table, "mac")
	d.Remark = field.NewString(table, "remark")
	d.UserId = field.NewUint64(table, "user_id")
	d.IntelligentAgentID = field.NewUint64(table, "intelligent_agent_id")
	d.OTAEnabled = field.NewBool(table, "ota_enabled")
	d.OTAChannel = field.NewInt(table, "ota_channel")
	d.Code = field.NewString(table, "code")
	d.Identifier = field.NewString(table, "identifier")
	d.SystemVersion = field.NewString(table, "system_version")

	d.fillFieldMap()

	return d
}

func (d *device) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *device) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 16)
	d.fieldMap["id"] = d.ID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["mac"] = d.MAC
	d.fieldMap["remark"] = d.Remark
	d.fieldMap["user_id"] = d.UserId
	d.fieldMap["intelligent_agent_id"] = d.IntelligentAgentID
	d.fieldMap["ota_enabled"] = d.OTAEnabled
	d.fieldMap["ota_channel"] = d.OTAChannel
	d.fieldMap["code"] = d.Code
	d.fieldMap["identifier"] = d.Identifier
	d.fieldMap["system_version"] = d.SystemVersion

}

func (d device) clone(db *gorm.DB) device {
	d.deviceDo.ReplaceConnPool(db.Statement.ConnPool)
	d.DeviceType.db = db.Session(&gorm.Session{Initialized: true})
	d.DeviceType.db.Statement.ConnPool = db.Statement.ConnPool
	d.User.db = db.Session(&gorm.Session{Initialized: true})
	d.User.db.Statement.ConnPool = db.Statement.ConnPool
	d.IntelligentAgent.db = db.Session(&gorm.Session{Initialized: true})
	d.IntelligentAgent.db.Statement.ConnPool = db.Statement.ConnPool
	return d
}

func (d device) replaceDB(db *gorm.DB) device {
	d.deviceDo.ReplaceDB(db)
	d.DeviceType.db = db.Session(&gorm.Session{})
	d.User.db = db.Session(&gorm.Session{})
	d.IntelligentAgent.db = db.Session(&gorm.Session{})
	return d
}

type deviceHasOneDeviceType struct {
	db *gorm.DB

	field.RelationField

	DefaultRole struct {
		field.RelationField
		Img struct {
			field.RelationField
			User struct {
				field.RelationField
				Avatar struct {
					field.RelationField
				}
				UserGroup struct {
					field.RelationField
				}
			}
		}
	}
}

func (a deviceHasOneDeviceType) Where(conds ...field.Expr) *deviceHasOneDeviceType {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a deviceHasOneDeviceType) WithContext(ctx context.Context) *deviceHasOneDeviceType {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a deviceHasOneDeviceType) Session(session *gorm.Session) *deviceHasOneDeviceType {
	a.db = a.db.Session(session)
	return &a
}

func (a deviceHasOneDeviceType) Model(m *model.Device) *deviceHasOneDeviceTypeTx {
	return &deviceHasOneDeviceTypeTx{a.db.Model(m).Association(a.Name())}
}

func (a deviceHasOneDeviceType) Unscoped() *deviceHasOneDeviceType {
	a.db = a.db.Unscoped()
	return &a
}

type deviceHasOneDeviceTypeTx struct{ tx *gorm.Association }

func (a deviceHasOneDeviceTypeTx) Find() (result *model.DeviceType, err error) {
	return result, a.tx.Find(&result)
}

func (a deviceHasOneDeviceTypeTx) Append(values ...*model.DeviceType) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a deviceHasOneDeviceTypeTx) Replace(values ...*model.DeviceType) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a deviceHasOneDeviceTypeTx) Delete(values ...*model.DeviceType) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a deviceHasOneDeviceTypeTx) Clear() error {
	return a.tx.Clear()
}

func (a deviceHasOneDeviceTypeTx) Count() int64 {
	return a.tx.Count()
}

func (a deviceHasOneDeviceTypeTx) Unscoped() *deviceHasOneDeviceTypeTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type deviceBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a deviceBelongsToUser) Where(conds ...field.Expr) *deviceBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a deviceBelongsToUser) WithContext(ctx context.Context) *deviceBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a deviceBelongsToUser) Session(session *gorm.Session) *deviceBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a deviceBelongsToUser) Model(m *model.Device) *deviceBelongsToUserTx {
	return &deviceBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a deviceBelongsToUser) Unscoped() *deviceBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type deviceBelongsToUserTx struct{ tx *gorm.Association }

func (a deviceBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a deviceBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a deviceBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a deviceBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a deviceBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a deviceBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a deviceBelongsToUserTx) Unscoped() *deviceBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type deviceBelongsToIntelligentAgent struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
	}
	Img struct {
		field.RelationField
	}
	RoleTemplate struct {
		field.RelationField
	}
	Devices struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		IntelligentAgent struct {
			field.RelationField
		}
		DeviceType struct {
			field.RelationField
		}
	}
}

func (a deviceBelongsToIntelligentAgent) Where(conds ...field.Expr) *deviceBelongsToIntelligentAgent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a deviceBelongsToIntelligentAgent) WithContext(ctx context.Context) *deviceBelongsToIntelligentAgent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a deviceBelongsToIntelligentAgent) Session(session *gorm.Session) *deviceBelongsToIntelligentAgent {
	a.db = a.db.Session(session)
	return &a
}

func (a deviceBelongsToIntelligentAgent) Model(m *model.Device) *deviceBelongsToIntelligentAgentTx {
	return &deviceBelongsToIntelligentAgentTx{a.db.Model(m).Association(a.Name())}
}

func (a deviceBelongsToIntelligentAgent) Unscoped() *deviceBelongsToIntelligentAgent {
	a.db = a.db.Unscoped()
	return &a
}

type deviceBelongsToIntelligentAgentTx struct{ tx *gorm.Association }

func (a deviceBelongsToIntelligentAgentTx) Find() (result *model.IntelligentAgent, err error) {
	return result, a.tx.Find(&result)
}

func (a deviceBelongsToIntelligentAgentTx) Append(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a deviceBelongsToIntelligentAgentTx) Replace(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a deviceBelongsToIntelligentAgentTx) Delete(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a deviceBelongsToIntelligentAgentTx) Clear() error {
	return a.tx.Clear()
}

func (a deviceBelongsToIntelligentAgentTx) Count() int64 {
	return a.tx.Count()
}

func (a deviceBelongsToIntelligentAgentTx) Unscoped() *deviceBelongsToIntelligentAgentTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type deviceDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (d deviceDo) FirstByID(id uint64) (result *model.Device, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (d deviceDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update devices set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = d.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (d deviceDo) Debug() *deviceDo {
	return d.withDO(d.DO.Debug())
}

func (d deviceDo) WithContext(ctx context.Context) *deviceDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d deviceDo) ReadDB() *deviceDo {
	return d.Clauses(dbresolver.Read)
}

func (d deviceDo) WriteDB() *deviceDo {
	return d.Clauses(dbresolver.Write)
}

func (d deviceDo) Session(config *gorm.Session) *deviceDo {
	return d.withDO(d.DO.Session(config))
}

func (d deviceDo) Clauses(conds ...clause.Expression) *deviceDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d deviceDo) Returning(value interface{}, columns ...string) *deviceDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d deviceDo) Not(conds ...gen.Condition) *deviceDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d deviceDo) Or(conds ...gen.Condition) *deviceDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d deviceDo) Select(conds ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d deviceDo) Where(conds ...gen.Condition) *deviceDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d deviceDo) Order(conds ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d deviceDo) Distinct(cols ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d deviceDo) Omit(cols ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d deviceDo) Join(table schema.Tabler, on ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d deviceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *deviceDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d deviceDo) RightJoin(table schema.Tabler, on ...field.Expr) *deviceDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d deviceDo) Group(cols ...field.Expr) *deviceDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d deviceDo) Having(conds ...gen.Condition) *deviceDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d deviceDo) Limit(limit int) *deviceDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d deviceDo) Offset(offset int) *deviceDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d deviceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *deviceDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d deviceDo) Unscoped() *deviceDo {
	return d.withDO(d.DO.Unscoped())
}

func (d deviceDo) Create(values ...*model.Device) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d deviceDo) CreateInBatches(values []*model.Device, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d deviceDo) Save(values ...*model.Device) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d deviceDo) First() (*model.Device, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Device), nil
	}
}

func (d deviceDo) Take() (*model.Device, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Device), nil
	}
}

func (d deviceDo) Last() (*model.Device, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Device), nil
	}
}

func (d deviceDo) Find() ([]*model.Device, error) {
	result, err := d.DO.Find()
	return result.([]*model.Device), err
}

func (d deviceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Device, err error) {
	buf := make([]*model.Device, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d deviceDo) FindInBatches(result *[]*model.Device, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d deviceDo) Attrs(attrs ...field.AssignExpr) *deviceDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d deviceDo) Assign(attrs ...field.AssignExpr) *deviceDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d deviceDo) Joins(fields ...field.RelationField) *deviceDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d deviceDo) Preload(fields ...field.RelationField) *deviceDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d deviceDo) FirstOrInit() (*model.Device, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Device), nil
	}
}

func (d deviceDo) FirstOrCreate() (*model.Device, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Device), nil
	}
}

func (d deviceDo) FindByPage(offset int, limit int) (result []*model.Device, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d deviceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d deviceDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d deviceDo) Delete(models ...*model.Device) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *deviceDo) withDO(do gen.Dao) *deviceDo {
	d.DO = *do.(*gen.DO)
	return d
}
