// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newChat(db *gorm.DB, opts ...gen.DOOption) chat {
	_chat := chat{}

	_chat.chatDo.UseDB(db, opts...)
	_chat.chatDo.UseModel(&model.Chat{})

	tableName := _chat.chatDo.TableName()
	_chat.ALL = field.NewAsterisk(tableName)
	_chat.ID = field.NewUint64(tableName, "id")
	_chat.CreatedAt = field.NewUint64(tableName, "created_at")
	_chat.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_chat.DeletedAt = field.NewUint(tableName, "deleted_at")
	_chat.UserID = field.NewUint64(tableName, "user_id")
	_chat.AgentID = field.NewUint64(tableName, "agent_id")
	_chat.DeviceID = field.NewUint64(tableName, "device_id")
	_chat.SessionID = field.NewString(tableName, "session_id")
	_chat.Title = field.NewString(tableName, "title")
	_chat.Messages = chatHasManyMessages{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Messages", "model.ChatMessage"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Messages.User", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Messages.User.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Messages.User.Avatar.User", "model.User"),
				},
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Messages.User.UserGroup", "model.UserGroup"),
			},
		},
		Chat: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Agent struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Img struct {
					field.RelationField
				}
				RoleTemplate struct {
					field.RelationField
					Img struct {
						field.RelationField
					}
				}
				Devices struct {
					field.RelationField
					User struct {
						field.RelationField
					}
					IntelligentAgent struct {
						field.RelationField
					}
					DeviceType struct {
						field.RelationField
						DefaultRole struct {
							field.RelationField
						}
					}
				}
			}
			Device struct {
				field.RelationField
			}
			Messages struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Messages.Chat", "model.Chat"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Messages.Chat.User", "model.User"),
			},
			Agent: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				Img struct {
					field.RelationField
				}
				RoleTemplate struct {
					field.RelationField
					Img struct {
						field.RelationField
					}
				}
				Devices struct {
					field.RelationField
					User struct {
						field.RelationField
					}
					IntelligentAgent struct {
						field.RelationField
					}
					DeviceType struct {
						field.RelationField
						DefaultRole struct {
							field.RelationField
						}
					}
				}
			}{
				RelationField: field.NewRelation("Messages.Chat.Agent", "model.IntelligentAgent"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Messages.Chat.Agent.User", "model.User"),
				},
				Img: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Messages.Chat.Agent.Img", "model.Upload"),
				},
				RoleTemplate: struct {
					field.RelationField
					Img struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Messages.Chat.Agent.RoleTemplate", "model.RoleTemplate"),
					Img: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Messages.Chat.Agent.RoleTemplate.Img", "model.Upload"),
					},
				},
				Devices: struct {
					field.RelationField
					User struct {
						field.RelationField
					}
					IntelligentAgent struct {
						field.RelationField
					}
					DeviceType struct {
						field.RelationField
						DefaultRole struct {
							field.RelationField
						}
					}
				}{
					RelationField: field.NewRelation("Messages.Chat.Agent.Devices", "model.Device"),
					User: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Messages.Chat.Agent.Devices.User", "model.User"),
					},
					IntelligentAgent: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Messages.Chat.Agent.Devices.IntelligentAgent", "model.IntelligentAgent"),
					},
					DeviceType: struct {
						field.RelationField
						DefaultRole struct {
							field.RelationField
						}
					}{
						RelationField: field.NewRelation("Messages.Chat.Agent.Devices.DeviceType", "model.DeviceType"),
						DefaultRole: struct {
							field.RelationField
						}{
							RelationField: field.NewRelation("Messages.Chat.Agent.Devices.DeviceType.DefaultRole", "model.RoleTemplate"),
						},
					},
				},
			},
			Device: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Messages.Chat.Device", "model.Device"),
			},
			Messages: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Messages.Chat.Messages", "model.ChatMessage"),
			},
		},
	}

	_chat.User = chatBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_chat.Agent = chatBelongsToAgent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Agent", "model.IntelligentAgent"),
	}

	_chat.Device = chatBelongsToDevice{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Device", "model.Device"),
	}

	_chat.fillFieldMap()

	return _chat
}

type chat struct {
	chatDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	UserID    field.Uint64
	AgentID   field.Uint64
	DeviceID  field.Uint64
	SessionID field.String
	Title     field.String
	Messages  chatHasManyMessages

	User chatBelongsToUser

	Agent chatBelongsToAgent

	Device chatBelongsToDevice

	fieldMap map[string]field.Expr
}

func (c chat) Table(newTableName string) *chat {
	c.chatDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chat) As(alias string) *chat {
	c.chatDo.DO = *(c.chatDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chat) updateTableName(table string) *chat {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.UserID = field.NewUint64(table, "user_id")
	c.AgentID = field.NewUint64(table, "agent_id")
	c.DeviceID = field.NewUint64(table, "device_id")
	c.SessionID = field.NewString(table, "session_id")
	c.Title = field.NewString(table, "title")

	c.fillFieldMap()

	return c
}

func (c *chat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chat) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 13)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["agent_id"] = c.AgentID
	c.fieldMap["device_id"] = c.DeviceID
	c.fieldMap["session_id"] = c.SessionID
	c.fieldMap["title"] = c.Title

}

func (c chat) clone(db *gorm.DB) chat {
	c.chatDo.ReplaceConnPool(db.Statement.ConnPool)
	c.Messages.db = db.Session(&gorm.Session{Initialized: true})
	c.Messages.db.Statement.ConnPool = db.Statement.ConnPool
	c.User.db = db.Session(&gorm.Session{Initialized: true})
	c.User.db.Statement.ConnPool = db.Statement.ConnPool
	c.Agent.db = db.Session(&gorm.Session{Initialized: true})
	c.Agent.db.Statement.ConnPool = db.Statement.ConnPool
	c.Device.db = db.Session(&gorm.Session{Initialized: true})
	c.Device.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c chat) replaceDB(db *gorm.DB) chat {
	c.chatDo.ReplaceDB(db)
	c.Messages.db = db.Session(&gorm.Session{})
	c.User.db = db.Session(&gorm.Session{})
	c.Agent.db = db.Session(&gorm.Session{})
	c.Device.db = db.Session(&gorm.Session{})
	return c
}

type chatHasManyMessages struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		UserGroup struct {
			field.RelationField
		}
	}
	Chat struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Agent struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Img struct {
				field.RelationField
			}
			RoleTemplate struct {
				field.RelationField
				Img struct {
					field.RelationField
				}
			}
			Devices struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				IntelligentAgent struct {
					field.RelationField
				}
				DeviceType struct {
					field.RelationField
					DefaultRole struct {
						field.RelationField
					}
				}
			}
		}
		Device struct {
			field.RelationField
		}
		Messages struct {
			field.RelationField
		}
	}
}

func (a chatHasManyMessages) Where(conds ...field.Expr) *chatHasManyMessages {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatHasManyMessages) WithContext(ctx context.Context) *chatHasManyMessages {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatHasManyMessages) Session(session *gorm.Session) *chatHasManyMessages {
	a.db = a.db.Session(session)
	return &a
}

func (a chatHasManyMessages) Model(m *model.Chat) *chatHasManyMessagesTx {
	return &chatHasManyMessagesTx{a.db.Model(m).Association(a.Name())}
}

func (a chatHasManyMessages) Unscoped() *chatHasManyMessages {
	a.db = a.db.Unscoped()
	return &a
}

type chatHasManyMessagesTx struct{ tx *gorm.Association }

func (a chatHasManyMessagesTx) Find() (result []*model.ChatMessage, err error) {
	return result, a.tx.Find(&result)
}

func (a chatHasManyMessagesTx) Append(values ...*model.ChatMessage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatHasManyMessagesTx) Replace(values ...*model.ChatMessage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatHasManyMessagesTx) Delete(values ...*model.ChatMessage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatHasManyMessagesTx) Clear() error {
	return a.tx.Clear()
}

func (a chatHasManyMessagesTx) Count() int64 {
	return a.tx.Count()
}

func (a chatHasManyMessagesTx) Unscoped() *chatHasManyMessagesTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a chatBelongsToUser) Where(conds ...field.Expr) *chatBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatBelongsToUser) WithContext(ctx context.Context) *chatBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatBelongsToUser) Session(session *gorm.Session) *chatBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a chatBelongsToUser) Model(m *model.Chat) *chatBelongsToUserTx {
	return &chatBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a chatBelongsToUser) Unscoped() *chatBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type chatBelongsToUserTx struct{ tx *gorm.Association }

func (a chatBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a chatBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a chatBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a chatBelongsToUserTx) Unscoped() *chatBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatBelongsToAgent struct {
	db *gorm.DB

	field.RelationField
}

func (a chatBelongsToAgent) Where(conds ...field.Expr) *chatBelongsToAgent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatBelongsToAgent) WithContext(ctx context.Context) *chatBelongsToAgent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatBelongsToAgent) Session(session *gorm.Session) *chatBelongsToAgent {
	a.db = a.db.Session(session)
	return &a
}

func (a chatBelongsToAgent) Model(m *model.Chat) *chatBelongsToAgentTx {
	return &chatBelongsToAgentTx{a.db.Model(m).Association(a.Name())}
}

func (a chatBelongsToAgent) Unscoped() *chatBelongsToAgent {
	a.db = a.db.Unscoped()
	return &a
}

type chatBelongsToAgentTx struct{ tx *gorm.Association }

func (a chatBelongsToAgentTx) Find() (result *model.IntelligentAgent, err error) {
	return result, a.tx.Find(&result)
}

func (a chatBelongsToAgentTx) Append(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatBelongsToAgentTx) Replace(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatBelongsToAgentTx) Delete(values ...*model.IntelligentAgent) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatBelongsToAgentTx) Clear() error {
	return a.tx.Clear()
}

func (a chatBelongsToAgentTx) Count() int64 {
	return a.tx.Count()
}

func (a chatBelongsToAgentTx) Unscoped() *chatBelongsToAgentTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatBelongsToDevice struct {
	db *gorm.DB

	field.RelationField
}

func (a chatBelongsToDevice) Where(conds ...field.Expr) *chatBelongsToDevice {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatBelongsToDevice) WithContext(ctx context.Context) *chatBelongsToDevice {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatBelongsToDevice) Session(session *gorm.Session) *chatBelongsToDevice {
	a.db = a.db.Session(session)
	return &a
}

func (a chatBelongsToDevice) Model(m *model.Chat) *chatBelongsToDeviceTx {
	return &chatBelongsToDeviceTx{a.db.Model(m).Association(a.Name())}
}

func (a chatBelongsToDevice) Unscoped() *chatBelongsToDevice {
	a.db = a.db.Unscoped()
	return &a
}

type chatBelongsToDeviceTx struct{ tx *gorm.Association }

func (a chatBelongsToDeviceTx) Find() (result *model.Device, err error) {
	return result, a.tx.Find(&result)
}

func (a chatBelongsToDeviceTx) Append(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatBelongsToDeviceTx) Replace(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatBelongsToDeviceTx) Delete(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatBelongsToDeviceTx) Clear() error {
	return a.tx.Clear()
}

func (a chatBelongsToDeviceTx) Count() int64 {
	return a.tx.Count()
}

func (a chatBelongsToDeviceTx) Unscoped() *chatBelongsToDeviceTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c chatDo) FirstByID(id uint64) (result *model.Chat, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c chatDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update chats set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c chatDo) Debug() *chatDo {
	return c.withDO(c.DO.Debug())
}

func (c chatDo) WithContext(ctx context.Context) *chatDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chatDo) ReadDB() *chatDo {
	return c.Clauses(dbresolver.Read)
}

func (c chatDo) WriteDB() *chatDo {
	return c.Clauses(dbresolver.Write)
}

func (c chatDo) Session(config *gorm.Session) *chatDo {
	return c.withDO(c.DO.Session(config))
}

func (c chatDo) Clauses(conds ...clause.Expression) *chatDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chatDo) Returning(value interface{}, columns ...string) *chatDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chatDo) Not(conds ...gen.Condition) *chatDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chatDo) Or(conds ...gen.Condition) *chatDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chatDo) Select(conds ...field.Expr) *chatDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chatDo) Where(conds ...gen.Condition) *chatDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chatDo) Order(conds ...field.Expr) *chatDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chatDo) Distinct(cols ...field.Expr) *chatDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chatDo) Omit(cols ...field.Expr) *chatDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chatDo) Join(table schema.Tabler, on ...field.Expr) *chatDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chatDo) LeftJoin(table schema.Tabler, on ...field.Expr) *chatDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chatDo) RightJoin(table schema.Tabler, on ...field.Expr) *chatDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chatDo) Group(cols ...field.Expr) *chatDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chatDo) Having(conds ...gen.Condition) *chatDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chatDo) Limit(limit int) *chatDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chatDo) Offset(offset int) *chatDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *chatDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chatDo) Unscoped() *chatDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chatDo) Create(values ...*model.Chat) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chatDo) CreateInBatches(values []*model.Chat, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chatDo) Save(values ...*model.Chat) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chatDo) First() (*model.Chat, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Chat), nil
	}
}

func (c chatDo) Take() (*model.Chat, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Chat), nil
	}
}

func (c chatDo) Last() (*model.Chat, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Chat), nil
	}
}

func (c chatDo) Find() ([]*model.Chat, error) {
	result, err := c.DO.Find()
	return result.([]*model.Chat), err
}

func (c chatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Chat, err error) {
	buf := make([]*model.Chat, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chatDo) FindInBatches(result *[]*model.Chat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chatDo) Attrs(attrs ...field.AssignExpr) *chatDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chatDo) Assign(attrs ...field.AssignExpr) *chatDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chatDo) Joins(fields ...field.RelationField) *chatDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chatDo) Preload(fields ...field.RelationField) *chatDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chatDo) FirstOrInit() (*model.Chat, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Chat), nil
	}
}

func (c chatDo) FirstOrCreate() (*model.Chat, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Chat), nil
	}
}

func (c chatDo) FindByPage(offset int, limit int) (result []*model.Chat, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chatDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chatDo) Delete(models ...*model.Chat) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chatDo) withDO(do gen.Dao) *chatDo {
	c.DO = *do.(*gen.DO)
	return c
}
