// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newPost(db *gorm.DB, opts ...gen.DOOption) post {
	_post := post{}

	_post.postDo.UseDB(db, opts...)
	_post.postDo.UseModel(&model.Post{})

	tableName := _post.postDo.TableName()
	_post.ALL = field.NewAsterisk(tableName)
	_post.ID = field.NewUint64(tableName, "id")
	_post.CreatedAt = field.NewUint64(tableName, "created_at")
	_post.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_post.DeletedAt = field.NewUint(tableName, "deleted_at")
	_post.Title = field.NewString(tableName, "title")
	_post.Content = field.NewString(tableName, "content")
	_post.PostCategoryId = field.NewUint64(tableName, "post_category_id")
	_post.Type = field.NewString(tableName, "type")
	_post.UserId = field.NewUint64(tableName, "user_id")
	_post.BannerId = field.NewUint64(tableName, "banner_id")
	_post.Alias_ = field.NewString(tableName, "alias")
	_post.Keywords = field.NewString(tableName, "keywords")
	_post.ContentType = field.NewString(tableName, "content_type")
	_post.Abstract = field.NewString(tableName, "abstract")
	_post.Status = field.NewString(tableName, "status")
	_post.PostCategory = postBelongsToPostCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("PostCategory", "model.PostCategory"),
		Banner: struct {
			field.RelationField
			User struct {
				field.RelationField
				Avatar struct {
					field.RelationField
				}
				UserGroup struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("PostCategory.Banner", "model.Upload"),
			User: struct {
				field.RelationField
				Avatar struct {
					field.RelationField
				}
				UserGroup struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("PostCategory.Banner.User", "model.User"),
				Avatar: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PostCategory.Banner.User.Avatar", "model.Upload"),
				},
				UserGroup: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("PostCategory.Banner.User.UserGroup", "model.UserGroup"),
				},
			},
		},
	}

	_post.User = postBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_post.Banner = postBelongsToBanner{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Banner", "model.Upload"),
	}

	_post.fillFieldMap()

	return _post
}

type post struct {
	postDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Uint64
	UpdatedAt      field.Uint64
	DeletedAt      field.Uint
	Title          field.String
	Content        field.String
	PostCategoryId field.Uint64
	Type           field.String
	UserId         field.Uint64
	BannerId       field.Uint64
	Alias_         field.String
	Keywords       field.String
	ContentType    field.String
	Abstract       field.String
	Status         field.String
	PostCategory   postBelongsToPostCategory

	User postBelongsToUser

	Banner postBelongsToBanner

	fieldMap map[string]field.Expr
}

func (p post) Table(newTableName string) *post {
	p.postDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p post) As(alias string) *post {
	p.postDo.DO = *(p.postDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *post) updateTableName(table string) *post {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewUint64(table, "created_at")
	p.UpdatedAt = field.NewUint64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.Title = field.NewString(table, "title")
	p.Content = field.NewString(table, "content")
	p.PostCategoryId = field.NewUint64(table, "post_category_id")
	p.Type = field.NewString(table, "type")
	p.UserId = field.NewUint64(table, "user_id")
	p.BannerId = field.NewUint64(table, "banner_id")
	p.Alias_ = field.NewString(table, "alias")
	p.Keywords = field.NewString(table, "keywords")
	p.ContentType = field.NewString(table, "content_type")
	p.Abstract = field.NewString(table, "abstract")
	p.Status = field.NewString(table, "status")

	p.fillFieldMap()

	return p
}

func (p *post) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *post) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 18)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["title"] = p.Title
	p.fieldMap["content"] = p.Content
	p.fieldMap["post_category_id"] = p.PostCategoryId
	p.fieldMap["type"] = p.Type
	p.fieldMap["user_id"] = p.UserId
	p.fieldMap["banner_id"] = p.BannerId
	p.fieldMap["alias"] = p.Alias_
	p.fieldMap["keywords"] = p.Keywords
	p.fieldMap["content_type"] = p.ContentType
	p.fieldMap["abstract"] = p.Abstract
	p.fieldMap["status"] = p.Status

}

func (p post) clone(db *gorm.DB) post {
	p.postDo.ReplaceConnPool(db.Statement.ConnPool)
	p.PostCategory.db = db.Session(&gorm.Session{Initialized: true})
	p.PostCategory.db.Statement.ConnPool = db.Statement.ConnPool
	p.User.db = db.Session(&gorm.Session{Initialized: true})
	p.User.db.Statement.ConnPool = db.Statement.ConnPool
	p.Banner.db = db.Session(&gorm.Session{Initialized: true})
	p.Banner.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p post) replaceDB(db *gorm.DB) post {
	p.postDo.ReplaceDB(db)
	p.PostCategory.db = db.Session(&gorm.Session{})
	p.User.db = db.Session(&gorm.Session{})
	p.Banner.db = db.Session(&gorm.Session{})
	return p
}

type postBelongsToPostCategory struct {
	db *gorm.DB

	field.RelationField

	Banner struct {
		field.RelationField
		User struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}
	}
}

func (a postBelongsToPostCategory) Where(conds ...field.Expr) *postBelongsToPostCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a postBelongsToPostCategory) WithContext(ctx context.Context) *postBelongsToPostCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a postBelongsToPostCategory) Session(session *gorm.Session) *postBelongsToPostCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a postBelongsToPostCategory) Model(m *model.Post) *postBelongsToPostCategoryTx {
	return &postBelongsToPostCategoryTx{a.db.Model(m).Association(a.Name())}
}

func (a postBelongsToPostCategory) Unscoped() *postBelongsToPostCategory {
	a.db = a.db.Unscoped()
	return &a
}

type postBelongsToPostCategoryTx struct{ tx *gorm.Association }

func (a postBelongsToPostCategoryTx) Find() (result *model.PostCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a postBelongsToPostCategoryTx) Append(values ...*model.PostCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a postBelongsToPostCategoryTx) Replace(values ...*model.PostCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a postBelongsToPostCategoryTx) Delete(values ...*model.PostCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a postBelongsToPostCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a postBelongsToPostCategoryTx) Count() int64 {
	return a.tx.Count()
}

func (a postBelongsToPostCategoryTx) Unscoped() *postBelongsToPostCategoryTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type postBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a postBelongsToUser) Where(conds ...field.Expr) *postBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a postBelongsToUser) WithContext(ctx context.Context) *postBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a postBelongsToUser) Session(session *gorm.Session) *postBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a postBelongsToUser) Model(m *model.Post) *postBelongsToUserTx {
	return &postBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a postBelongsToUser) Unscoped() *postBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type postBelongsToUserTx struct{ tx *gorm.Association }

func (a postBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a postBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a postBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a postBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a postBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a postBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a postBelongsToUserTx) Unscoped() *postBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type postBelongsToBanner struct {
	db *gorm.DB

	field.RelationField
}

func (a postBelongsToBanner) Where(conds ...field.Expr) *postBelongsToBanner {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a postBelongsToBanner) WithContext(ctx context.Context) *postBelongsToBanner {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a postBelongsToBanner) Session(session *gorm.Session) *postBelongsToBanner {
	a.db = a.db.Session(session)
	return &a
}

func (a postBelongsToBanner) Model(m *model.Post) *postBelongsToBannerTx {
	return &postBelongsToBannerTx{a.db.Model(m).Association(a.Name())}
}

func (a postBelongsToBanner) Unscoped() *postBelongsToBanner {
	a.db = a.db.Unscoped()
	return &a
}

type postBelongsToBannerTx struct{ tx *gorm.Association }

func (a postBelongsToBannerTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a postBelongsToBannerTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a postBelongsToBannerTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a postBelongsToBannerTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a postBelongsToBannerTx) Clear() error {
	return a.tx.Clear()
}

func (a postBelongsToBannerTx) Count() int64 {
	return a.tx.Count()
}

func (a postBelongsToBannerTx) Unscoped() *postBelongsToBannerTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type postDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p postDo) FirstByID(id uint64) (result *model.Post, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p postDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update posts set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p postDo) Debug() *postDo {
	return p.withDO(p.DO.Debug())
}

func (p postDo) WithContext(ctx context.Context) *postDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p postDo) ReadDB() *postDo {
	return p.Clauses(dbresolver.Read)
}

func (p postDo) WriteDB() *postDo {
	return p.Clauses(dbresolver.Write)
}

func (p postDo) Session(config *gorm.Session) *postDo {
	return p.withDO(p.DO.Session(config))
}

func (p postDo) Clauses(conds ...clause.Expression) *postDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p postDo) Returning(value interface{}, columns ...string) *postDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p postDo) Not(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p postDo) Or(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p postDo) Select(conds ...field.Expr) *postDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p postDo) Where(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p postDo) Order(conds ...field.Expr) *postDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p postDo) Distinct(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p postDo) Omit(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p postDo) Join(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p postDo) LeftJoin(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p postDo) RightJoin(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p postDo) Group(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p postDo) Having(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p postDo) Limit(limit int) *postDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p postDo) Offset(offset int) *postDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p postDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *postDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p postDo) Unscoped() *postDo {
	return p.withDO(p.DO.Unscoped())
}

func (p postDo) Create(values ...*model.Post) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p postDo) CreateInBatches(values []*model.Post, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p postDo) Save(values ...*model.Post) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p postDo) First() (*model.Post, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Post), nil
	}
}

func (p postDo) Take() (*model.Post, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Post), nil
	}
}

func (p postDo) Last() (*model.Post, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Post), nil
	}
}

func (p postDo) Find() ([]*model.Post, error) {
	result, err := p.DO.Find()
	return result.([]*model.Post), err
}

func (p postDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Post, err error) {
	buf := make([]*model.Post, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p postDo) FindInBatches(result *[]*model.Post, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p postDo) Attrs(attrs ...field.AssignExpr) *postDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p postDo) Assign(attrs ...field.AssignExpr) *postDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p postDo) Joins(fields ...field.RelationField) *postDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p postDo) Preload(fields ...field.RelationField) *postDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p postDo) FirstOrInit() (*model.Post, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Post), nil
	}
}

func (p postDo) FirstOrCreate() (*model.Post, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Post), nil
	}
}

func (p postDo) FindByPage(offset int, limit int) (result []*model.Post, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p postDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p postDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p postDo) Delete(models ...*model.Post) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *postDo) withDO(do gen.Dao) *postDo {
	p.DO = *do.(*gen.DO)
	return p
}
