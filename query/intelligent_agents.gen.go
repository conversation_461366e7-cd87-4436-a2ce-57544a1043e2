// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newIntelligentAgent(db *gorm.DB, opts ...gen.DOOption) intelligentAgent {
	_intelligentAgent := intelligentAgent{}

	_intelligentAgent.intelligentAgentDo.UseDB(db, opts...)
	_intelligentAgent.intelligentAgentDo.UseModel(&model.IntelligentAgent{})

	tableName := _intelligentAgent.intelligentAgentDo.TableName()
	_intelligentAgent.ALL = field.NewAsterisk(tableName)
	_intelligentAgent.ID = field.NewUint64(tableName, "id")
	_intelligentAgent.CreatedAt = field.NewUint64(tableName, "created_at")
	_intelligentAgent.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_intelligentAgent.DeletedAt = field.NewUint(tableName, "deleted_at")
	_intelligentAgent.Name = field.NewString(tableName, "name")
	_intelligentAgent.Description = field.NewString(tableName, "description")
	_intelligentAgent.NickName = field.NewString(tableName, "nick_name")
	_intelligentAgent.UserID = field.NewUint64(tableName, "user_id")
	_intelligentAgent.ImgID = field.NewUint64(tableName, "img_id")
	_intelligentAgent.LLM = field.NewString(tableName, "llm")
	_intelligentAgent.Memory = field.NewString(tableName, "memory")
	_intelligentAgent.Prompt = field.NewString(tableName, "prompt")
	_intelligentAgent.VoiceType = field.NewString(tableName, "voice_type")
	_intelligentAgent.Language = field.NewString(tableName, "language")
	_intelligentAgent.LastChat = field.NewInt64(tableName, "last_chat")
	_intelligentAgent.RoleTemplateID = field.NewUint64(tableName, "role_template_id")
	_intelligentAgent.Devices = intelligentAgentHasManyDevices{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Devices", "model.Device"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Devices.User", "model.User"),
			Avatar: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Devices.User.Avatar", "model.Upload"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Devices.User.Avatar.User", "model.User"),
				},
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Devices.User.UserGroup", "model.UserGroup"),
			},
		},
		IntelligentAgent: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Img struct {
				field.RelationField
			}
			RoleTemplate struct {
				field.RelationField
				Img struct {
					field.RelationField
				}
			}
			Devices struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Devices.IntelligentAgent", "model.IntelligentAgent"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Devices.IntelligentAgent.User", "model.User"),
			},
			Img: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Devices.IntelligentAgent.Img", "model.Upload"),
			},
			RoleTemplate: struct {
				field.RelationField
				Img struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Devices.IntelligentAgent.RoleTemplate", "model.RoleTemplate"),
				Img: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Devices.IntelligentAgent.RoleTemplate.Img", "model.Upload"),
				},
			},
			Devices: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Devices.IntelligentAgent.Devices", "model.Device"),
			},
		},
		DeviceType: struct {
			field.RelationField
			DefaultRole struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Devices.DeviceType", "model.DeviceType"),
			DefaultRole: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Devices.DeviceType.DefaultRole", "model.RoleTemplate"),
			},
		},
	}

	_intelligentAgent.User = intelligentAgentBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
	}

	_intelligentAgent.Img = intelligentAgentBelongsToImg{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Img", "model.Upload"),
	}

	_intelligentAgent.RoleTemplate = intelligentAgentBelongsToRoleTemplate{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("RoleTemplate", "model.RoleTemplate"),
	}

	_intelligentAgent.fillFieldMap()

	return _intelligentAgent
}

type intelligentAgent struct {
	intelligentAgentDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Uint64
	UpdatedAt      field.Uint64
	DeletedAt      field.Uint
	Name           field.String
	Description    field.String
	NickName       field.String
	UserID         field.Uint64
	ImgID          field.Uint64
	LLM            field.String
	Memory         field.String
	Prompt         field.String
	VoiceType      field.String
	Language       field.String
	LastChat       field.Int64
	RoleTemplateID field.Uint64
	Devices        intelligentAgentHasManyDevices

	User intelligentAgentBelongsToUser

	Img intelligentAgentBelongsToImg

	RoleTemplate intelligentAgentBelongsToRoleTemplate

	fieldMap map[string]field.Expr
}

func (i intelligentAgent) Table(newTableName string) *intelligentAgent {
	i.intelligentAgentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i intelligentAgent) As(alias string) *intelligentAgent {
	i.intelligentAgentDo.DO = *(i.intelligentAgentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *intelligentAgent) updateTableName(table string) *intelligentAgent {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewUint64(table, "created_at")
	i.UpdatedAt = field.NewUint64(table, "updated_at")
	i.DeletedAt = field.NewUint(table, "deleted_at")
	i.Name = field.NewString(table, "name")
	i.Description = field.NewString(table, "description")
	i.NickName = field.NewString(table, "nick_name")
	i.UserID = field.NewUint64(table, "user_id")
	i.ImgID = field.NewUint64(table, "img_id")
	i.LLM = field.NewString(table, "llm")
	i.Memory = field.NewString(table, "memory")
	i.Prompt = field.NewString(table, "prompt")
	i.VoiceType = field.NewString(table, "voice_type")
	i.Language = field.NewString(table, "language")
	i.LastChat = field.NewInt64(table, "last_chat")
	i.RoleTemplateID = field.NewUint64(table, "role_template_id")

	i.fillFieldMap()

	return i
}

func (i *intelligentAgent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *intelligentAgent) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 20)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["description"] = i.Description
	i.fieldMap["nick_name"] = i.NickName
	i.fieldMap["user_id"] = i.UserID
	i.fieldMap["img_id"] = i.ImgID
	i.fieldMap["llm"] = i.LLM
	i.fieldMap["memory"] = i.Memory
	i.fieldMap["prompt"] = i.Prompt
	i.fieldMap["voice_type"] = i.VoiceType
	i.fieldMap["language"] = i.Language
	i.fieldMap["last_chat"] = i.LastChat
	i.fieldMap["role_template_id"] = i.RoleTemplateID

}

func (i intelligentAgent) clone(db *gorm.DB) intelligentAgent {
	i.intelligentAgentDo.ReplaceConnPool(db.Statement.ConnPool)
	i.Devices.db = db.Session(&gorm.Session{Initialized: true})
	i.Devices.db.Statement.ConnPool = db.Statement.ConnPool
	i.User.db = db.Session(&gorm.Session{Initialized: true})
	i.User.db.Statement.ConnPool = db.Statement.ConnPool
	i.Img.db = db.Session(&gorm.Session{Initialized: true})
	i.Img.db.Statement.ConnPool = db.Statement.ConnPool
	i.RoleTemplate.db = db.Session(&gorm.Session{Initialized: true})
	i.RoleTemplate.db.Statement.ConnPool = db.Statement.ConnPool
	return i
}

func (i intelligentAgent) replaceDB(db *gorm.DB) intelligentAgent {
	i.intelligentAgentDo.ReplaceDB(db)
	i.Devices.db = db.Session(&gorm.Session{})
	i.User.db = db.Session(&gorm.Session{})
	i.Img.db = db.Session(&gorm.Session{})
	i.RoleTemplate.db = db.Session(&gorm.Session{})
	return i
}

type intelligentAgentHasManyDevices struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}
		UserGroup struct {
			field.RelationField
		}
	}
	IntelligentAgent struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Img struct {
			field.RelationField
		}
		RoleTemplate struct {
			field.RelationField
			Img struct {
				field.RelationField
			}
		}
		Devices struct {
			field.RelationField
		}
	}
	DeviceType struct {
		field.RelationField
		DefaultRole struct {
			field.RelationField
		}
	}
}

func (a intelligentAgentHasManyDevices) Where(conds ...field.Expr) *intelligentAgentHasManyDevices {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a intelligentAgentHasManyDevices) WithContext(ctx context.Context) *intelligentAgentHasManyDevices {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a intelligentAgentHasManyDevices) Session(session *gorm.Session) *intelligentAgentHasManyDevices {
	a.db = a.db.Session(session)
	return &a
}

func (a intelligentAgentHasManyDevices) Model(m *model.IntelligentAgent) *intelligentAgentHasManyDevicesTx {
	return &intelligentAgentHasManyDevicesTx{a.db.Model(m).Association(a.Name())}
}

func (a intelligentAgentHasManyDevices) Unscoped() *intelligentAgentHasManyDevices {
	a.db = a.db.Unscoped()
	return &a
}

type intelligentAgentHasManyDevicesTx struct{ tx *gorm.Association }

func (a intelligentAgentHasManyDevicesTx) Find() (result []*model.Device, err error) {
	return result, a.tx.Find(&result)
}

func (a intelligentAgentHasManyDevicesTx) Append(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a intelligentAgentHasManyDevicesTx) Replace(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a intelligentAgentHasManyDevicesTx) Delete(values ...*model.Device) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a intelligentAgentHasManyDevicesTx) Clear() error {
	return a.tx.Clear()
}

func (a intelligentAgentHasManyDevicesTx) Count() int64 {
	return a.tx.Count()
}

func (a intelligentAgentHasManyDevicesTx) Unscoped() *intelligentAgentHasManyDevicesTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type intelligentAgentBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a intelligentAgentBelongsToUser) Where(conds ...field.Expr) *intelligentAgentBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a intelligentAgentBelongsToUser) WithContext(ctx context.Context) *intelligentAgentBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a intelligentAgentBelongsToUser) Session(session *gorm.Session) *intelligentAgentBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a intelligentAgentBelongsToUser) Model(m *model.IntelligentAgent) *intelligentAgentBelongsToUserTx {
	return &intelligentAgentBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a intelligentAgentBelongsToUser) Unscoped() *intelligentAgentBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type intelligentAgentBelongsToUserTx struct{ tx *gorm.Association }

func (a intelligentAgentBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a intelligentAgentBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a intelligentAgentBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a intelligentAgentBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a intelligentAgentBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a intelligentAgentBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a intelligentAgentBelongsToUserTx) Unscoped() *intelligentAgentBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type intelligentAgentBelongsToImg struct {
	db *gorm.DB

	field.RelationField
}

func (a intelligentAgentBelongsToImg) Where(conds ...field.Expr) *intelligentAgentBelongsToImg {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a intelligentAgentBelongsToImg) WithContext(ctx context.Context) *intelligentAgentBelongsToImg {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a intelligentAgentBelongsToImg) Session(session *gorm.Session) *intelligentAgentBelongsToImg {
	a.db = a.db.Session(session)
	return &a
}

func (a intelligentAgentBelongsToImg) Model(m *model.IntelligentAgent) *intelligentAgentBelongsToImgTx {
	return &intelligentAgentBelongsToImgTx{a.db.Model(m).Association(a.Name())}
}

func (a intelligentAgentBelongsToImg) Unscoped() *intelligentAgentBelongsToImg {
	a.db = a.db.Unscoped()
	return &a
}

type intelligentAgentBelongsToImgTx struct{ tx *gorm.Association }

func (a intelligentAgentBelongsToImgTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a intelligentAgentBelongsToImgTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a intelligentAgentBelongsToImgTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a intelligentAgentBelongsToImgTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a intelligentAgentBelongsToImgTx) Clear() error {
	return a.tx.Clear()
}

func (a intelligentAgentBelongsToImgTx) Count() int64 {
	return a.tx.Count()
}

func (a intelligentAgentBelongsToImgTx) Unscoped() *intelligentAgentBelongsToImgTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type intelligentAgentBelongsToRoleTemplate struct {
	db *gorm.DB

	field.RelationField
}

func (a intelligentAgentBelongsToRoleTemplate) Where(conds ...field.Expr) *intelligentAgentBelongsToRoleTemplate {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a intelligentAgentBelongsToRoleTemplate) WithContext(ctx context.Context) *intelligentAgentBelongsToRoleTemplate {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a intelligentAgentBelongsToRoleTemplate) Session(session *gorm.Session) *intelligentAgentBelongsToRoleTemplate {
	a.db = a.db.Session(session)
	return &a
}

func (a intelligentAgentBelongsToRoleTemplate) Model(m *model.IntelligentAgent) *intelligentAgentBelongsToRoleTemplateTx {
	return &intelligentAgentBelongsToRoleTemplateTx{a.db.Model(m).Association(a.Name())}
}

func (a intelligentAgentBelongsToRoleTemplate) Unscoped() *intelligentAgentBelongsToRoleTemplate {
	a.db = a.db.Unscoped()
	return &a
}

type intelligentAgentBelongsToRoleTemplateTx struct{ tx *gorm.Association }

func (a intelligentAgentBelongsToRoleTemplateTx) Find() (result *model.RoleTemplate, err error) {
	return result, a.tx.Find(&result)
}

func (a intelligentAgentBelongsToRoleTemplateTx) Append(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a intelligentAgentBelongsToRoleTemplateTx) Replace(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a intelligentAgentBelongsToRoleTemplateTx) Delete(values ...*model.RoleTemplate) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a intelligentAgentBelongsToRoleTemplateTx) Clear() error {
	return a.tx.Clear()
}

func (a intelligentAgentBelongsToRoleTemplateTx) Count() int64 {
	return a.tx.Count()
}

func (a intelligentAgentBelongsToRoleTemplateTx) Unscoped() *intelligentAgentBelongsToRoleTemplateTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type intelligentAgentDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (i intelligentAgentDo) FirstByID(id uint64) (result *model.IntelligentAgent, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (i intelligentAgentDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update intelligent_agents set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = i.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (i intelligentAgentDo) Debug() *intelligentAgentDo {
	return i.withDO(i.DO.Debug())
}

func (i intelligentAgentDo) WithContext(ctx context.Context) *intelligentAgentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i intelligentAgentDo) ReadDB() *intelligentAgentDo {
	return i.Clauses(dbresolver.Read)
}

func (i intelligentAgentDo) WriteDB() *intelligentAgentDo {
	return i.Clauses(dbresolver.Write)
}

func (i intelligentAgentDo) Session(config *gorm.Session) *intelligentAgentDo {
	return i.withDO(i.DO.Session(config))
}

func (i intelligentAgentDo) Clauses(conds ...clause.Expression) *intelligentAgentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i intelligentAgentDo) Returning(value interface{}, columns ...string) *intelligentAgentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i intelligentAgentDo) Not(conds ...gen.Condition) *intelligentAgentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i intelligentAgentDo) Or(conds ...gen.Condition) *intelligentAgentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i intelligentAgentDo) Select(conds ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i intelligentAgentDo) Where(conds ...gen.Condition) *intelligentAgentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i intelligentAgentDo) Order(conds ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i intelligentAgentDo) Distinct(cols ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i intelligentAgentDo) Omit(cols ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i intelligentAgentDo) Join(table schema.Tabler, on ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i intelligentAgentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i intelligentAgentDo) RightJoin(table schema.Tabler, on ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i intelligentAgentDo) Group(cols ...field.Expr) *intelligentAgentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i intelligentAgentDo) Having(conds ...gen.Condition) *intelligentAgentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i intelligentAgentDo) Limit(limit int) *intelligentAgentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i intelligentAgentDo) Offset(offset int) *intelligentAgentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i intelligentAgentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *intelligentAgentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i intelligentAgentDo) Unscoped() *intelligentAgentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i intelligentAgentDo) Create(values ...*model.IntelligentAgent) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i intelligentAgentDo) CreateInBatches(values []*model.IntelligentAgent, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i intelligentAgentDo) Save(values ...*model.IntelligentAgent) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i intelligentAgentDo) First() (*model.IntelligentAgent, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.IntelligentAgent), nil
	}
}

func (i intelligentAgentDo) Take() (*model.IntelligentAgent, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.IntelligentAgent), nil
	}
}

func (i intelligentAgentDo) Last() (*model.IntelligentAgent, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.IntelligentAgent), nil
	}
}

func (i intelligentAgentDo) Find() ([]*model.IntelligentAgent, error) {
	result, err := i.DO.Find()
	return result.([]*model.IntelligentAgent), err
}

func (i intelligentAgentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.IntelligentAgent, err error) {
	buf := make([]*model.IntelligentAgent, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i intelligentAgentDo) FindInBatches(result *[]*model.IntelligentAgent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i intelligentAgentDo) Attrs(attrs ...field.AssignExpr) *intelligentAgentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i intelligentAgentDo) Assign(attrs ...field.AssignExpr) *intelligentAgentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i intelligentAgentDo) Joins(fields ...field.RelationField) *intelligentAgentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i intelligentAgentDo) Preload(fields ...field.RelationField) *intelligentAgentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i intelligentAgentDo) FirstOrInit() (*model.IntelligentAgent, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.IntelligentAgent), nil
	}
}

func (i intelligentAgentDo) FirstOrCreate() (*model.IntelligentAgent, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.IntelligentAgent), nil
	}
}

func (i intelligentAgentDo) FindByPage(offset int, limit int) (result []*model.IntelligentAgent, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i intelligentAgentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i intelligentAgentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i intelligentAgentDo) Delete(models ...*model.IntelligentAgent) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *intelligentAgentDo) withDO(do gen.Dao) *intelligentAgentDo {
	i.DO = *do.(*gen.DO)
	return i
}
