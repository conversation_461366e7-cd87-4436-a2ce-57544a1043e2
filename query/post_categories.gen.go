// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newPostCategory(db *gorm.DB, opts ...gen.DOOption) postCategory {
	_postCategory := postCategory{}

	_postCategory.postCategoryDo.UseDB(db, opts...)
	_postCategory.postCategoryDo.UseModel(&model.PostCategory{})

	tableName := _postCategory.postCategoryDo.TableName()
	_postCategory.ALL = field.NewAsterisk(tableName)
	_postCategory.ID = field.NewUint64(tableName, "id")
	_postCategory.CreatedAt = field.NewUint64(tableName, "created_at")
	_postCategory.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_postCategory.DeletedAt = field.NewUint(tableName, "deleted_at")
	_postCategory.Name = field.NewString(tableName, "name")
	_postCategory.Description = field.NewString(tableName, "description")
	_postCategory.BannerID = field.NewUint64(tableName, "banner_id")
	_postCategory.Banner = postCategoryBelongsToBanner{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Banner", "model.Upload"),
		User: struct {
			field.RelationField
			Avatar struct {
				field.RelationField
			}
			UserGroup struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Banner.User", "model.User"),
			Avatar: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Banner.User.Avatar", "model.Upload"),
			},
			UserGroup: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Banner.User.UserGroup", "model.UserGroup"),
			},
		},
	}

	_postCategory.fillFieldMap()

	return _postCategory
}

type postCategory struct {
	postCategoryDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Uint64
	UpdatedAt   field.Uint64
	DeletedAt   field.Uint
	Name        field.String
	Description field.String
	BannerID    field.Uint64
	Banner      postCategoryBelongsToBanner

	fieldMap map[string]field.Expr
}

func (p postCategory) Table(newTableName string) *postCategory {
	p.postCategoryDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p postCategory) As(alias string) *postCategory {
	p.postCategoryDo.DO = *(p.postCategoryDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *postCategory) updateTableName(table string) *postCategory {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewUint64(table, "id")
	p.CreatedAt = field.NewUint64(table, "created_at")
	p.UpdatedAt = field.NewUint64(table, "updated_at")
	p.DeletedAt = field.NewUint(table, "deleted_at")
	p.Name = field.NewString(table, "name")
	p.Description = field.NewString(table, "description")
	p.BannerID = field.NewUint64(table, "banner_id")

	p.fillFieldMap()

	return p
}

func (p *postCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *postCategory) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 8)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["name"] = p.Name
	p.fieldMap["description"] = p.Description
	p.fieldMap["banner_id"] = p.BannerID

}

func (p postCategory) clone(db *gorm.DB) postCategory {
	p.postCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	p.Banner.db = db.Session(&gorm.Session{Initialized: true})
	p.Banner.db.Statement.ConnPool = db.Statement.ConnPool
	return p
}

func (p postCategory) replaceDB(db *gorm.DB) postCategory {
	p.postCategoryDo.ReplaceDB(db)
	p.Banner.db = db.Session(&gorm.Session{})
	return p
}

type postCategoryBelongsToBanner struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Avatar struct {
			field.RelationField
		}
		UserGroup struct {
			field.RelationField
		}
	}
}

func (a postCategoryBelongsToBanner) Where(conds ...field.Expr) *postCategoryBelongsToBanner {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a postCategoryBelongsToBanner) WithContext(ctx context.Context) *postCategoryBelongsToBanner {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a postCategoryBelongsToBanner) Session(session *gorm.Session) *postCategoryBelongsToBanner {
	a.db = a.db.Session(session)
	return &a
}

func (a postCategoryBelongsToBanner) Model(m *model.PostCategory) *postCategoryBelongsToBannerTx {
	return &postCategoryBelongsToBannerTx{a.db.Model(m).Association(a.Name())}
}

func (a postCategoryBelongsToBanner) Unscoped() *postCategoryBelongsToBanner {
	a.db = a.db.Unscoped()
	return &a
}

type postCategoryBelongsToBannerTx struct{ tx *gorm.Association }

func (a postCategoryBelongsToBannerTx) Find() (result *model.Upload, err error) {
	return result, a.tx.Find(&result)
}

func (a postCategoryBelongsToBannerTx) Append(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a postCategoryBelongsToBannerTx) Replace(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a postCategoryBelongsToBannerTx) Delete(values ...*model.Upload) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a postCategoryBelongsToBannerTx) Clear() error {
	return a.tx.Clear()
}

func (a postCategoryBelongsToBannerTx) Count() int64 {
	return a.tx.Count()
}

func (a postCategoryBelongsToBannerTx) Unscoped() *postCategoryBelongsToBannerTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type postCategoryDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (p postCategoryDo) FirstByID(id uint64) (result *model.PostCategory, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (p postCategoryDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update post_categories set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p postCategoryDo) Debug() *postCategoryDo {
	return p.withDO(p.DO.Debug())
}

func (p postCategoryDo) WithContext(ctx context.Context) *postCategoryDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p postCategoryDo) ReadDB() *postCategoryDo {
	return p.Clauses(dbresolver.Read)
}

func (p postCategoryDo) WriteDB() *postCategoryDo {
	return p.Clauses(dbresolver.Write)
}

func (p postCategoryDo) Session(config *gorm.Session) *postCategoryDo {
	return p.withDO(p.DO.Session(config))
}

func (p postCategoryDo) Clauses(conds ...clause.Expression) *postCategoryDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p postCategoryDo) Returning(value interface{}, columns ...string) *postCategoryDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p postCategoryDo) Not(conds ...gen.Condition) *postCategoryDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p postCategoryDo) Or(conds ...gen.Condition) *postCategoryDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p postCategoryDo) Select(conds ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p postCategoryDo) Where(conds ...gen.Condition) *postCategoryDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p postCategoryDo) Order(conds ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p postCategoryDo) Distinct(cols ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p postCategoryDo) Omit(cols ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p postCategoryDo) Join(table schema.Tabler, on ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p postCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p postCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p postCategoryDo) Group(cols ...field.Expr) *postCategoryDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p postCategoryDo) Having(conds ...gen.Condition) *postCategoryDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p postCategoryDo) Limit(limit int) *postCategoryDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p postCategoryDo) Offset(offset int) *postCategoryDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p postCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *postCategoryDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p postCategoryDo) Unscoped() *postCategoryDo {
	return p.withDO(p.DO.Unscoped())
}

func (p postCategoryDo) Create(values ...*model.PostCategory) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p postCategoryDo) CreateInBatches(values []*model.PostCategory, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p postCategoryDo) Save(values ...*model.PostCategory) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p postCategoryDo) First() (*model.PostCategory, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostCategory), nil
	}
}

func (p postCategoryDo) Take() (*model.PostCategory, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostCategory), nil
	}
}

func (p postCategoryDo) Last() (*model.PostCategory, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostCategory), nil
	}
}

func (p postCategoryDo) Find() ([]*model.PostCategory, error) {
	result, err := p.DO.Find()
	return result.([]*model.PostCategory), err
}

func (p postCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PostCategory, err error) {
	buf := make([]*model.PostCategory, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p postCategoryDo) FindInBatches(result *[]*model.PostCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p postCategoryDo) Attrs(attrs ...field.AssignExpr) *postCategoryDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p postCategoryDo) Assign(attrs ...field.AssignExpr) *postCategoryDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p postCategoryDo) Joins(fields ...field.RelationField) *postCategoryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p postCategoryDo) Preload(fields ...field.RelationField) *postCategoryDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p postCategoryDo) FirstOrInit() (*model.PostCategory, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostCategory), nil
	}
}

func (p postCategoryDo) FirstOrCreate() (*model.PostCategory, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostCategory), nil
	}
}

func (p postCategoryDo) FindByPage(offset int, limit int) (result []*model.PostCategory, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p postCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p postCategoryDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p postCategoryDo) Delete(models ...*model.PostCategory) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *postCategoryDo) withDO(do gen.Dao) *postCategoryDo {
	p.DO = *do.(*gen.DO)
	return p
}
