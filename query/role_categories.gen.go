// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newRoleCategory(db *gorm.DB, opts ...gen.DOOption) roleCategory {
	_roleCategory := roleCategory{}

	_roleCategory.roleCategoryDo.UseDB(db, opts...)
	_roleCategory.roleCategoryDo.UseModel(&model.RoleCategory{})

	tableName := _roleCategory.roleCategoryDo.TableName()
	_roleCategory.ALL = field.NewAsterisk(tableName)
	_roleCategory.ID = field.NewUint64(tableName, "id")
	_roleCategory.CreatedAt = field.NewUint64(tableName, "created_at")
	_roleCategory.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_roleCategory.DeletedAt = field.NewUint(tableName, "deleted_at")
	_roleCategory.Name = field.NewString(tableName, "name")
	_roleCategory.OrderID = field.NewInt(tableName, "order_id")

	_roleCategory.fillFieldMap()

	return _roleCategory
}

type roleCategory struct {
	roleCategoryDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Uint64
	UpdatedAt field.Uint64
	DeletedAt field.Uint
	Name      field.String
	OrderID   field.Int

	fieldMap map[string]field.Expr
}

func (r roleCategory) Table(newTableName string) *roleCategory {
	r.roleCategoryDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r roleCategory) As(alias string) *roleCategory {
	r.roleCategoryDo.DO = *(r.roleCategoryDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *roleCategory) updateTableName(table string) *roleCategory {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewUint64(table, "id")
	r.CreatedAt = field.NewUint64(table, "created_at")
	r.UpdatedAt = field.NewUint64(table, "updated_at")
	r.DeletedAt = field.NewUint(table, "deleted_at")
	r.Name = field.NewString(table, "name")
	r.OrderID = field.NewInt(table, "order_id")

	r.fillFieldMap()

	return r
}

func (r *roleCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *roleCategory) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 6)
	r.fieldMap["id"] = r.ID
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["deleted_at"] = r.DeletedAt
	r.fieldMap["name"] = r.Name
	r.fieldMap["order_id"] = r.OrderID
}

func (r roleCategory) clone(db *gorm.DB) roleCategory {
	r.roleCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r roleCategory) replaceDB(db *gorm.DB) roleCategory {
	r.roleCategoryDo.ReplaceDB(db)
	return r
}

type roleCategoryDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (r roleCategoryDo) FirstByID(id uint64) (result *model.RoleCategory, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (r roleCategoryDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update role_categories set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (r roleCategoryDo) Debug() *roleCategoryDo {
	return r.withDO(r.DO.Debug())
}

func (r roleCategoryDo) WithContext(ctx context.Context) *roleCategoryDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r roleCategoryDo) ReadDB() *roleCategoryDo {
	return r.Clauses(dbresolver.Read)
}

func (r roleCategoryDo) WriteDB() *roleCategoryDo {
	return r.Clauses(dbresolver.Write)
}

func (r roleCategoryDo) Session(config *gorm.Session) *roleCategoryDo {
	return r.withDO(r.DO.Session(config))
}

func (r roleCategoryDo) Clauses(conds ...clause.Expression) *roleCategoryDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r roleCategoryDo) Returning(value interface{}, columns ...string) *roleCategoryDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r roleCategoryDo) Not(conds ...gen.Condition) *roleCategoryDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r roleCategoryDo) Or(conds ...gen.Condition) *roleCategoryDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r roleCategoryDo) Select(conds ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r roleCategoryDo) Where(conds ...gen.Condition) *roleCategoryDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r roleCategoryDo) Order(conds ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r roleCategoryDo) Distinct(cols ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r roleCategoryDo) Omit(cols ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r roleCategoryDo) Join(table schema.Tabler, on ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r roleCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r roleCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r roleCategoryDo) Group(cols ...field.Expr) *roleCategoryDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r roleCategoryDo) Having(conds ...gen.Condition) *roleCategoryDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r roleCategoryDo) Limit(limit int) *roleCategoryDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r roleCategoryDo) Offset(offset int) *roleCategoryDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r roleCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *roleCategoryDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r roleCategoryDo) Unscoped() *roleCategoryDo {
	return r.withDO(r.DO.Unscoped())
}

func (r roleCategoryDo) Create(values ...*model.RoleCategory) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r roleCategoryDo) CreateInBatches(values []*model.RoleCategory, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r roleCategoryDo) Save(values ...*model.RoleCategory) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r roleCategoryDo) First() (*model.RoleCategory, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleCategory), nil
	}
}

func (r roleCategoryDo) Take() (*model.RoleCategory, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleCategory), nil
	}
}

func (r roleCategoryDo) Last() (*model.RoleCategory, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleCategory), nil
	}
}

func (r roleCategoryDo) Find() ([]*model.RoleCategory, error) {
	result, err := r.DO.Find()
	return result.([]*model.RoleCategory), err
}

func (r roleCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.RoleCategory, err error) {
	buf := make([]*model.RoleCategory, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r roleCategoryDo) FindInBatches(result *[]*model.RoleCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r roleCategoryDo) Attrs(attrs ...field.AssignExpr) *roleCategoryDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r roleCategoryDo) Assign(attrs ...field.AssignExpr) *roleCategoryDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r roleCategoryDo) Joins(fields ...field.RelationField) *roleCategoryDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r roleCategoryDo) Preload(fields ...field.RelationField) *roleCategoryDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r roleCategoryDo) FirstOrInit() (*model.RoleCategory, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleCategory), nil
	}
}

func (r roleCategoryDo) FirstOrCreate() (*model.RoleCategory, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.RoleCategory), nil
	}
}

func (r roleCategoryDo) FindByPage(offset int, limit int) (result []*model.RoleCategory, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r roleCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r roleCategoryDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r roleCategoryDo) Delete(models ...*model.RoleCategory) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *roleCategoryDo) withDO(do gen.Dao) *roleCategoryDo {
	r.DO = *do.(*gen.DO)
	return r
}
