// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-api/model"
)

func newChatMessage(db *gorm.DB, opts ...gen.DOOption) chatMessage {
	_chatMessage := chatMessage{}

	_chatMessage.chatMessageDo.UseDB(db, opts...)
	_chatMessage.chatMessageDo.UseModel(&model.ChatMessage{})

	tableName := _chatMessage.chatMessageDo.TableName()
	_chatMessage.ALL = field.NewAsterisk(tableName)
	_chatMessage.ID = field.NewUint64(tableName, "id")
	_chatMessage.CreatedAt = field.NewUint64(tableName, "created_at")
	_chatMessage.UpdatedAt = field.NewUint64(tableName, "updated_at")
	_chatMessage.DeletedAt = field.NewUint(tableName, "deleted_at")
	_chatMessage.UserID = field.NewUint64(tableName, "user_id")
	_chatMessage.ChatID = field.NewUint64(tableName, "chat_id")
	_chatMessage.Role = field.NewString(tableName, "role")
	_chatMessage.Content = field.NewString(tableName, "content")
	_chatMessage.Embedding = field.NewString(tableName, "embedding")
	_chatMessage.AsrURL = field.NewString(tableName, "asr_url")
	_chatMessage.Name = field.NewString(tableName, "name")
	_chatMessage.LLM = field.NewString(tableName, "llm")
	_chatMessage.PromptTokens = field.NewUint(tableName, "prompt_tokens")
	_chatMessage.CompletionTokens = field.NewUint(tableName, "completion_tokens")
	_chatMessage.TotalTokens = field.NewUint(tableName, "total_tokens")
	_chatMessage.PromptMS = field.NewUint(tableName, "prompt_ms")
	_chatMessage.CompletionMS = field.NewUint(tableName, "completion_ms")
	_chatMessage.TotalMS = field.NewUint(tableName, "total_ms")
	_chatMessage.Length = field.NewUint(tableName, "length")
	_chatMessage.States = field.NewField(tableName, "states")
	_chatMessage.ToolCalls = field.NewString(tableName, "tool_calls")
	_chatMessage.ToolCallId = field.NewString(tableName, "tool_call_id")
	_chatMessage.User = chatMessageBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
		UserGroup: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.UserGroup", "model.UserGroup"),
		},
	}

	_chatMessage.Chat = chatMessageBelongsToChat{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Chat", "model.Chat"),
		User: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Chat.User", "model.User"),
		},
		Agent: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Img struct {
				field.RelationField
			}
			RoleTemplate struct {
				field.RelationField
				Img struct {
					field.RelationField
				}
			}
			Devices struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				IntelligentAgent struct {
					field.RelationField
				}
				DeviceType struct {
					field.RelationField
					DefaultRole struct {
						field.RelationField
					}
				}
			}
		}{
			RelationField: field.NewRelation("Chat.Agent", "model.IntelligentAgent"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Chat.Agent.User", "model.User"),
			},
			Img: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Chat.Agent.Img", "model.Upload"),
			},
			RoleTemplate: struct {
				field.RelationField
				Img struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Chat.Agent.RoleTemplate", "model.RoleTemplate"),
				Img: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Chat.Agent.RoleTemplate.Img", "model.Upload"),
				},
			},
			Devices: struct {
				field.RelationField
				User struct {
					field.RelationField
				}
				IntelligentAgent struct {
					field.RelationField
				}
				DeviceType struct {
					field.RelationField
					DefaultRole struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("Chat.Agent.Devices", "model.Device"),
				User: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Chat.Agent.Devices.User", "model.User"),
				},
				IntelligentAgent: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Chat.Agent.Devices.IntelligentAgent", "model.IntelligentAgent"),
				},
				DeviceType: struct {
					field.RelationField
					DefaultRole struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Chat.Agent.Devices.DeviceType", "model.DeviceType"),
					DefaultRole: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Chat.Agent.Devices.DeviceType.DefaultRole", "model.RoleTemplate"),
					},
				},
			},
		},
		Device: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Chat.Device", "model.Device"),
		},
		Messages: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			Chat struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Chat.Messages", "model.ChatMessage"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Chat.Messages.User", "model.User"),
			},
			Chat: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Chat.Messages.Chat", "model.Chat"),
			},
		},
	}

	_chatMessage.fillFieldMap()

	return _chatMessage
}

type chatMessage struct {
	chatMessageDo

	ALL              field.Asterisk
	ID               field.Uint64
	CreatedAt        field.Uint64
	UpdatedAt        field.Uint64
	DeletedAt        field.Uint
	UserID           field.Uint64
	ChatID           field.Uint64
	Role             field.String
	Content          field.String
	Embedding        field.String
	AsrURL           field.String
	Name             field.String
	LLM              field.String
	PromptTokens     field.Uint
	CompletionTokens field.Uint
	TotalTokens      field.Uint
	PromptMS         field.Uint
	CompletionMS     field.Uint
	TotalMS          field.Uint
	Length           field.Uint
	States           field.Field
	ToolCalls        field.String
	ToolCallId       field.String
	User             chatMessageBelongsToUser

	Chat chatMessageBelongsToChat

	fieldMap map[string]field.Expr
}

func (c chatMessage) Table(newTableName string) *chatMessage {
	c.chatMessageDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c chatMessage) As(alias string) *chatMessage {
	c.chatMessageDo.DO = *(c.chatMessageDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *chatMessage) updateTableName(table string) *chatMessage {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewUint64(table, "id")
	c.CreatedAt = field.NewUint64(table, "created_at")
	c.UpdatedAt = field.NewUint64(table, "updated_at")
	c.DeletedAt = field.NewUint(table, "deleted_at")
	c.UserID = field.NewUint64(table, "user_id")
	c.ChatID = field.NewUint64(table, "chat_id")
	c.Role = field.NewString(table, "role")
	c.Content = field.NewString(table, "content")
	c.Embedding = field.NewString(table, "embedding")
	c.AsrURL = field.NewString(table, "asr_url")
	c.Name = field.NewString(table, "name")
	c.LLM = field.NewString(table, "llm")
	c.PromptTokens = field.NewUint(table, "prompt_tokens")
	c.CompletionTokens = field.NewUint(table, "completion_tokens")
	c.TotalTokens = field.NewUint(table, "total_tokens")
	c.PromptMS = field.NewUint(table, "prompt_ms")
	c.CompletionMS = field.NewUint(table, "completion_ms")
	c.TotalMS = field.NewUint(table, "total_ms")
	c.Length = field.NewUint(table, "length")
	c.States = field.NewField(table, "states")
	c.ToolCalls = field.NewString(table, "tool_calls")
	c.ToolCallId = field.NewString(table, "tool_call_id")

	c.fillFieldMap()

	return c
}

func (c *chatMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *chatMessage) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 24)
	c.fieldMap["id"] = c.ID
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["deleted_at"] = c.DeletedAt
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["chat_id"] = c.ChatID
	c.fieldMap["role"] = c.Role
	c.fieldMap["content"] = c.Content
	c.fieldMap["embedding"] = c.Embedding
	c.fieldMap["asr_url"] = c.AsrURL
	c.fieldMap["name"] = c.Name
	c.fieldMap["llm"] = c.LLM
	c.fieldMap["prompt_tokens"] = c.PromptTokens
	c.fieldMap["completion_tokens"] = c.CompletionTokens
	c.fieldMap["total_tokens"] = c.TotalTokens
	c.fieldMap["prompt_ms"] = c.PromptMS
	c.fieldMap["completion_ms"] = c.CompletionMS
	c.fieldMap["total_ms"] = c.TotalMS
	c.fieldMap["length"] = c.Length
	c.fieldMap["states"] = c.States
	c.fieldMap["tool_calls"] = c.ToolCalls
	c.fieldMap["tool_call_id"] = c.ToolCallId

}

func (c chatMessage) clone(db *gorm.DB) chatMessage {
	c.chatMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	c.User.db = db.Session(&gorm.Session{Initialized: true})
	c.User.db.Statement.ConnPool = db.Statement.ConnPool
	c.Chat.db = db.Session(&gorm.Session{Initialized: true})
	c.Chat.db.Statement.ConnPool = db.Statement.ConnPool
	return c
}

func (c chatMessage) replaceDB(db *gorm.DB) chatMessage {
	c.chatMessageDo.ReplaceDB(db)
	c.User.db = db.Session(&gorm.Session{})
	c.Chat.db = db.Session(&gorm.Session{})
	return c
}

type chatMessageBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
	UserGroup struct {
		field.RelationField
	}
}

func (a chatMessageBelongsToUser) Where(conds ...field.Expr) *chatMessageBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatMessageBelongsToUser) WithContext(ctx context.Context) *chatMessageBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatMessageBelongsToUser) Session(session *gorm.Session) *chatMessageBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a chatMessageBelongsToUser) Model(m *model.ChatMessage) *chatMessageBelongsToUserTx {
	return &chatMessageBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a chatMessageBelongsToUser) Unscoped() *chatMessageBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type chatMessageBelongsToUserTx struct{ tx *gorm.Association }

func (a chatMessageBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a chatMessageBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatMessageBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatMessageBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatMessageBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a chatMessageBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a chatMessageBelongsToUserTx) Unscoped() *chatMessageBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatMessageBelongsToChat struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
	}
	Agent struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Img struct {
			field.RelationField
		}
		RoleTemplate struct {
			field.RelationField
			Img struct {
				field.RelationField
			}
		}
		Devices struct {
			field.RelationField
			User struct {
				field.RelationField
			}
			IntelligentAgent struct {
				field.RelationField
			}
			DeviceType struct {
				field.RelationField
				DefaultRole struct {
					field.RelationField
				}
			}
		}
	}
	Device struct {
		field.RelationField
	}
	Messages struct {
		field.RelationField
		User struct {
			field.RelationField
		}
		Chat struct {
			field.RelationField
		}
	}
}

func (a chatMessageBelongsToChat) Where(conds ...field.Expr) *chatMessageBelongsToChat {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a chatMessageBelongsToChat) WithContext(ctx context.Context) *chatMessageBelongsToChat {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a chatMessageBelongsToChat) Session(session *gorm.Session) *chatMessageBelongsToChat {
	a.db = a.db.Session(session)
	return &a
}

func (a chatMessageBelongsToChat) Model(m *model.ChatMessage) *chatMessageBelongsToChatTx {
	return &chatMessageBelongsToChatTx{a.db.Model(m).Association(a.Name())}
}

func (a chatMessageBelongsToChat) Unscoped() *chatMessageBelongsToChat {
	a.db = a.db.Unscoped()
	return &a
}

type chatMessageBelongsToChatTx struct{ tx *gorm.Association }

func (a chatMessageBelongsToChatTx) Find() (result *model.Chat, err error) {
	return result, a.tx.Find(&result)
}

func (a chatMessageBelongsToChatTx) Append(values ...*model.Chat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a chatMessageBelongsToChatTx) Replace(values ...*model.Chat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a chatMessageBelongsToChatTx) Delete(values ...*model.Chat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a chatMessageBelongsToChatTx) Clear() error {
	return a.tx.Clear()
}

func (a chatMessageBelongsToChatTx) Count() int64 {
	return a.tx.Count()
}

func (a chatMessageBelongsToChatTx) Unscoped() *chatMessageBelongsToChatTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type chatMessageDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (c chatMessageDo) FirstByID(id uint64) (result *model.ChatMessage, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (c chatMessageDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update chat_messages set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = c.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (c chatMessageDo) Debug() *chatMessageDo {
	return c.withDO(c.DO.Debug())
}

func (c chatMessageDo) WithContext(ctx context.Context) *chatMessageDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c chatMessageDo) ReadDB() *chatMessageDo {
	return c.Clauses(dbresolver.Read)
}

func (c chatMessageDo) WriteDB() *chatMessageDo {
	return c.Clauses(dbresolver.Write)
}

func (c chatMessageDo) Session(config *gorm.Session) *chatMessageDo {
	return c.withDO(c.DO.Session(config))
}

func (c chatMessageDo) Clauses(conds ...clause.Expression) *chatMessageDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c chatMessageDo) Returning(value interface{}, columns ...string) *chatMessageDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c chatMessageDo) Not(conds ...gen.Condition) *chatMessageDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c chatMessageDo) Or(conds ...gen.Condition) *chatMessageDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c chatMessageDo) Select(conds ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c chatMessageDo) Where(conds ...gen.Condition) *chatMessageDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c chatMessageDo) Order(conds ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c chatMessageDo) Distinct(cols ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c chatMessageDo) Omit(cols ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c chatMessageDo) Join(table schema.Tabler, on ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c chatMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c chatMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c chatMessageDo) Group(cols ...field.Expr) *chatMessageDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c chatMessageDo) Having(conds ...gen.Condition) *chatMessageDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c chatMessageDo) Limit(limit int) *chatMessageDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c chatMessageDo) Offset(offset int) *chatMessageDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c chatMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *chatMessageDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c chatMessageDo) Unscoped() *chatMessageDo {
	return c.withDO(c.DO.Unscoped())
}

func (c chatMessageDo) Create(values ...*model.ChatMessage) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c chatMessageDo) CreateInBatches(values []*model.ChatMessage, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c chatMessageDo) Save(values ...*model.ChatMessage) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c chatMessageDo) First() (*model.ChatMessage, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatMessage), nil
	}
}

func (c chatMessageDo) Take() (*model.ChatMessage, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatMessage), nil
	}
}

func (c chatMessageDo) Last() (*model.ChatMessage, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatMessage), nil
	}
}

func (c chatMessageDo) Find() ([]*model.ChatMessage, error) {
	result, err := c.DO.Find()
	return result.([]*model.ChatMessage), err
}

func (c chatMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ChatMessage, err error) {
	buf := make([]*model.ChatMessage, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c chatMessageDo) FindInBatches(result *[]*model.ChatMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c chatMessageDo) Attrs(attrs ...field.AssignExpr) *chatMessageDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c chatMessageDo) Assign(attrs ...field.AssignExpr) *chatMessageDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c chatMessageDo) Joins(fields ...field.RelationField) *chatMessageDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c chatMessageDo) Preload(fields ...field.RelationField) *chatMessageDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c chatMessageDo) FirstOrInit() (*model.ChatMessage, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatMessage), nil
	}
}

func (c chatMessageDo) FirstOrCreate() (*model.ChatMessage, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ChatMessage), nil
	}
}

func (c chatMessageDo) FindByPage(offset int, limit int) (result []*model.ChatMessage, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c chatMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c chatMessageDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c chatMessageDo) Delete(models ...*model.ChatMessage) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *chatMessageDo) withDO(do gen.Dao) *chatMessageDo {
	c.DO = *do.(*gen.DO)
	return c
}
