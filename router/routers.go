package router

import (
	"net/http"

	// "github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitRouter() {
	r := cosy.GetEngine()

	r.GET("/", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"message": "ok",
		})
	})

	initGlobalRouter()
	initDeviceRouter()
	initFrontendRouter()
	initAdminRouter()

	// Debug下使用pprof检测goroutine和heap / debug / pprof /
	// g := r.Group("/")
	// {
	// 	pprof.Register(g)
	// }

}
