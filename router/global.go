package router

import (
	"git.uozi.org/uozi/potato-api/api/global/auth"
	"git.uozi.org/uozi/potato-api/api/global/llm"
	"git.uozi.org/uozi/potato-api/api/global/upload"
	"git.uozi.org/uozi/potato-api/api/global/user"
	"git.uozi.org/uozi/potato-api/api/global/voice"
	"git.uozi.org/uozi/potato-api/api/wechat"
	"github.com/uozi-tech/cosy"
)

func initGlobalRouter() {
	r := cosy.GetEngine()
	auth.InitRouter(r)
	a := r.Group("/", AuthRequired())
	{
		user.InitRouter(a)
		upload.InitRouter(a)
		auth.InitAuthRouter(a)
		llm.InitRouter(a)
		voice.InitRouter(a)
		wechat.InitAuthRouter(a)
	}

	unauth := r.Group("/")
	{
		wechat.InitUnauthRouter(unauth)
	}
}
