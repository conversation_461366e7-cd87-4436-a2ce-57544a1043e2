package router

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api/admin/analytics"
	"git.uozi.org/uozi/potato-api/api/admin/chat"
	"git.uozi.org/uozi/potato-api/api/admin/device"
	"git.uozi.org/uozi/potato-api/api/admin/intelligent_agent"
	"git.uozi.org/uozi/potato-api/api/admin/media"
	"git.uozi.org/uozi/potato-api/api/admin/ota"
	"git.uozi.org/uozi/potato-api/api/admin/post"
	"git.uozi.org/uozi/potato-api/api/admin/role_template"
	"git.uozi.org/uozi/potato-api/api/admin/user"
	"git.uozi.org/uozi/potato-api/api/admin/voiceprint"
	"git.uozi.org/uozi/potato-api/statics"
	"github.com/uozi-tech/cosy"
)

func initAdminRouter() {
	r := cosy.GetEngine()

	g := r.Group("/admin", AuthRequired())
	{
		g.StaticFS("/statics", http.FS(statics.DistFS))
		user.InitUserRouter(g)
		user.InitUserGroupRouter(g)
		device.InitDeviceRouter(g)
		device.InitDeviceTypeRouter(g)
		role_template.InitRoleTemplateRouter(g)
		role_template.InitRoleCategoryRouter(g)
		intelligent_agent.InitIntelligentAgentRouter(g)
		voiceprint.InitVoiceprintRouter(g)
		ota.InitRouter(g)
		chat.InitRouter(g)
		media.InitRouter(g)
		post.InitPostRouter(g)
		post.InitPostCategoryRouter(g)
		analytics.InitRouter(g)
	}
}
