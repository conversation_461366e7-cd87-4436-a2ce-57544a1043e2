package router

import (
	"git.uozi.org/uozi/potato-api/api/frontend/chat"
	"git.uozi.org/uozi/potato-api/api/frontend/device"
	"git.uozi.org/uozi/potato-api/api/frontend/intelligent_agent"
	"git.uozi.org/uozi/potato-api/api/frontend/post"
	"git.uozi.org/uozi/potato-api/api/frontend/role_template"
	"git.uozi.org/uozi/potato-api/api/frontend/user"
	"git.uozi.org/uozi/potato-api/api/frontend/voiceprint"
	"github.com/uozi-tech/cosy"
)

func initFrontendRouter() {
	r := cosy.GetEngine()
	public := r.Group("/")
	{
		post.InitRouter(public)
		role_template.InitRoleTemplateRouter(public)
	}

	auth := r.Group("/", AuthRequired())
	{
		chat.InitRouter(auth)
		user.InitRouter(auth)
		
		intelligent_agent.InitIntelligentAgentRouter(auth)
		device.InitDeviceRouter(auth)
		voiceprint.InitVoiceprintRouter(auth)
	}
}
