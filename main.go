package main

import (
	"flag"

	"git.uozi.org/uozi/potato-api/internal/audit"

	"git.uozi.org/uozi/potato-api/internal/limiter"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/model/view"
	"git.uozi.org/uozi/potato-api/query"
	"git.uozi.org/uozi/potato-api/router"
	"github.com/uozi-tech/cosy"
	mysql "github.com/uozi-tech/cosy-driver-mysql"
	"github.com/uozi-tech/cosy/settings"

	"git.uozi.org/uozi/potato-api/internal/migrate"
)

type Config struct {
	ConfPath string
	Maintain string
}

var cfg Config

func init() {
	flag.StringVar(&cfg.ConfPath, "config", "app.ini", "Specify the configuration file")
	flag.Parse()
}

func main() {
	cosy.RegisterModels(model.GenerateAllModel()...)

	cosy.RegisterMigration(migrate.Migrations)

	cosy.RegisterInitFunc(func() {
		db := cosy.InitDB(mysql.Open(settings.DataBaseSettings))
		query.Init(db)
		model.Use(db)
		view.CreateViews(db)
		limiter.Init()
	},
		model.InitRuntimeSettings,
		router.InitRouter,
	)

	cosy.RegisterGoroutine(
		audit.Init,
	)

	cosy.Boot(cfg.ConfPath)

}
