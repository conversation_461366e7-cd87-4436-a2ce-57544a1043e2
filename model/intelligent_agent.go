package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// IntelligentAgent 智能体
type IntelligentAgent struct {
	Model
	Name           string        `json:"name" cosy:"add:required;update:omitempty;list:fussy" gorm:"index"`         // 智能体名称
	Description    string        `json:"description" cosy:"all:omitempty"`                                          // 智能体描述
	NickName       string        `json:"nick_name" cosy:"all:omitempty;list:fussy" gorm:"index"`                    // 对话时名字
	UserID         uint64        `json:"user_id,string" cosy:"add:omitempty;update:omitempty;list:eq" gorm:"index"` // 用户ID
	User           *User         `json:"user,omitempty" cosy:"item:preload;list:preload"`                           // 用户
	ImgID          uint64        `json:"img_id,string" cosy:"all:omitempty"`                                        // 图片 ID
	Img            *Upload       `json:"img,omitempty" cosy:"list:preload;item:preload"`                            // 图片
	LLM            string        `json:"llm" cosy:"all:omitempty;list:fussy" gorm:"index"`                          // LLM选择
	Memory         string        `json:"memory,omitempty" cosy:"add:omitempty;update:omitempty"`                    // 记忆体
	Prompt         string        `json:"prompt,omitempty" cosy:"add:omitempty;update:omitempty"`                    // 角色介绍，提示词，prompt
	VoiceType      string        `json:"voice_type" cosy:"add:required;update:omitempty;list:fussy" gorm:"index"`   // 音色
	Language       string        `json:"language" cosy:"all:omitempty;list:eq" gorm:"index"`                        // 语言偏好
	LastChat       int64         `json:"last_chat,omitempty"`                                                       // 最后对话时间
	Devices        []*Device     `json:"devices,omitempty"`                                                         // 绑定设备，可同时绑定多个设备
	RoleTemplateID uint64        `json:"role_template_id,string" cosy:"all:omitempty"`                              // 角色模板 ID
	RoleTemplate   *RoleTemplate `json:"role_template,omitempty" cosy:"item:preload;list:preload"`                  // 角色模板
}

func (a *IntelligentAgent) BeforeCreate(_ *gorm.DB) (err error) {
	if a.ID == 0 {
		a.ID = sonyflake.NextID()
	}
	return
}

func (a *IntelligentAgent) GetRole() *RoleTemplate {
	if a.RoleTemplateID != 0 && a.RoleTemplate == nil {
		db.Model(&RoleTemplate{}).
			Where("id = ?", a.RoleTemplateID).
			Preload("Img").
			Find(&a.RoleTemplate)
	}
	if a.RoleTemplate == nil || !a.RoleTemplate.Preset {
		return &RoleTemplate{
			Name:        a.Name,
			Description: a.Description,
			VoiceType:   a.VoiceType,
			Prompt:      a.Prompt,
			Img:         a.Img,
			Language:    a.Language,
		}
	}

	a.Name = a.RoleTemplate.Name
	a.Description = a.RoleTemplate.Description
	a.VoiceType = a.RoleTemplate.VoiceType
	a.Prompt = a.RoleTemplate.Prompt
	a.Img = a.RoleTemplate.Img
	a.Language = a.RoleTemplate.Language

	return a.RoleTemplate
}

type IntelligentAgents []*IntelligentAgent

func (l IntelligentAgents) OverrideByRoleTemplate() {
	var preloadIds []uint64
	for _, agent := range l {
		if agent.RoleTemplateID != 0 && agent.RoleTemplate == nil {
			preloadIds = append(preloadIds, agent.RoleTemplateID)
		}
	}
	var roleTemplates []*RoleTemplate
	if len(preloadIds) > 0 {
		db.Model(&RoleTemplate{}).
			Where("id IN ?", preloadIds).
			Preload("Img").
			Find(&roleTemplates)
	}
	roleTemplateMap := make(map[uint64]*RoleTemplate)
	for _, roleTemplate := range roleTemplates {
		roleTemplateMap[roleTemplate.ID] = roleTemplate
	}
	for _, agent := range l {
		if agent.RoleTemplateID == 0 {
			continue
		}
		if roleTemplate, ok := roleTemplateMap[agent.RoleTemplateID]; ok {
			agent.RoleTemplate = roleTemplate
		}
		agent.GetRole()
	}
}
