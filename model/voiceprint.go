package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// Voiceprint 声纹表
type Voiceprint struct {
	Model
	Name        string  `json:"name" cosy:"add:required;update:omitempty;list:fussy" gorm:"index"`           // 说话人名称
	Embedding   string  `json:"embedding" cosy:"add:required;update:omitempty;list:fussy"`                   // 声纹向量
	Salutation  string  `json:"salutation" cosy:"add:required;update:omitempty;list:fussy"`                  // 对说话人称呼
	Description string  `json:"description" cosy:"add:omitempty;update:omitempty;list:fussy"`                // 描述
	UserID      uint64  `json:"user_id,string" cosy:"add:required;update:omitempty;list:fussy" gorm:"index"` // 用户 ID
	User        *User   `json:"user,omitempty" cosy:"item:preload;list:preload"`                             // 用户
	ImgID       uint64  `json:"img_id,string" cosy:"all:omitempty;list:fussy"`                               // 图片 ID
	Img         *Upload `json:"img,omitempty" cosy:"item:preload;list:preload"`                              // 图片
}

func (v *Voiceprint) BeforeCreate(_ *gorm.DB) (err error) {
	if v.ID == 0 {
		v.ID = sonyflake.NextID()
	}
	return
}
