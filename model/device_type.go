package model

type DeviceType struct {
	Model
	Name          string        `json:"name" cosy:"add:required;update:required;list:fussy" gorm:"type:varchar(32);index"`
	Identifier    string        `json:"identifier" cosy:"all:required;list:fussy" gorm:"type:varchar(32);uniqueIndex"`
	DefaultRoleID uint64        `json:"default_role_id,string" cosy:"add:required;update:omitempty" gorm:"index"`
	DefaultRole   *RoleTemplate `json:"default_role" cosy:"list:preload;item:preload"`
}
