package model

// Device 设备表
type Device struct {
	Model
	MAC                string            `json:"mac" cosy:"add:required;update:omitempty;list:fussy" gorm:"unique;not null;check:mac <> ''"`
	Remark             string            `json:"remark" cosy:"all:omitempty;list:fussy"`
	UserId             uint64            `json:"user_id,string" cosy:"add:omitempty;update:omitempty;list:fussy" gorm:"default:0;index" `
	User               *User             `json:"user,omitempty" cosy:"item:preload;list:preload"`
	IntelligentAgentID uint64            `json:"intelligent_agent_id,string" cosy:"add:omitempty;update:omitempty;list:fussy" gorm:"index"`
	IntelligentAgent   *IntelligentAgent `json:"intelligent_agent,omitempty" cosy:"item:preload;list:preload"`
	OTAEnabled         bool              `json:"ota_enabled" cosy:"all:omitempty;list:eq" gorm:"default:true;index"`
	OTAChannel         OTAChannel        `json:"ota_channel" cosy:"all:omitempty;list:eq" gorm:"default:0;index"`
	Code               string            `json:"code" cosy:"all:omitempty;list:fussy" gorm:"index"`
	Identifier         string            `json:"identifier" cosy:"all:omitempty;list:fussy" gorm:"type:varchar(32);index"`
	SystemVersion      string            `json:"system_version" cosy:"list:eq"`
	DeviceType         *DeviceType       `json:"device_type,omitempty" cosy:"item:preload;list:preload" gorm:"foreignKey:Identifier;references:Identifier"`
}
