package view

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/uozi-tech/cosy/settings"
	"gorm.io/gorm"
)

const LatestChatMessageStatsViewName = "v_latest_chat_message_stats"

func GetLatestChatMessageStatsViewName() string {
	return settings.DataBaseSettings.TablePrefix + LatestChatMessageStatsViewName
}

// createLatestChatMessageStatsView creates a view for latest chat message statistics
func createLatestChatMessageStatsView(db *gorm.DB) error {
	stmt := gorm.Statement{DB: db}
	_ = stmt.Parse(&model.ChatMessage{})

	tableName := stmt.Table

	q := db.Raw(`
	SELECT
		SUM(prompt_tokens) as total_prompt_tokens,
		SUM(completion_tokens) as total_comp_tokens,
		SUM(total_tokens) as total_tokens,
		SUM(prompt_ms) as total_prompt_ms,
		SUM(completion_ms) as total_comp_ms,
		SUM(total_ms) as total_ms,
		AVG(prompt_ms) as avg_prompt_ms,
		AVG(completion_ms) as avg_comp_ms,
		AVG(total_ms) as avg_ms,
		SUM(IF(tool_calls IS NOT NULL, 1, 0)) as tool_call_count
	FROM (
		SELECT *
		FROM ` + "`" + tableName + "`" + `
		WHERE deleted_at = 0
		ORDER BY created_at DESC
		LIMIT 100
	) as latest_messages
	`)

	return db.Migrator().CreateView(GetLatestChatMessageStatsViewName(),
		gorm.ViewOption{Replace: true, Query: q})
}
