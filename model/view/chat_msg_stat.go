package view

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/uozi-tech/cosy/settings"
	"gorm.io/gorm"
)

const ChatMessageStatsViewName = "v_chat_message_stats"

func GetChatMessageStatsViewName() string {
	return settings.DataBaseSettings.TablePrefix + ChatMessageStatsViewName
}

// createChatMessageStatsView creates a view for daily chat message statistics
func createChatMessageStatsView(db *gorm.DB) error {
	stmt := gorm.Statement{DB: db}
	_ = stmt.Parse(&model.ChatMessage{})

	tableName := stmt.Table

	q := db.Raw(`
	SELECT
		FROM_UNIXTIME(created_at, '%Y-%m-%d') as date,
		COUNT(*) as message_count,
		SUM(prompt_tokens) as total_prompt_tokens,
		SUM(completion_tokens) as total_comp_tokens,
		SUM(total_tokens) as total_tokens,
		SUM(prompt_ms) as total_prompt_ms,
		SUM(completion_ms) as total_comp_ms,
		SUM(total_ms) as total_ms,
		AVG(prompt_ms) as avg_prompt_ms,
		AVG(completion_ms) as avg_comp_ms,
		AVG(total_ms) as avg_ms,
		SUM(IF(tool_calls IS NOT NULL, 1, 0)) as tool_call_count
	FROM ` + "`" + tableName + "`" + `
	WHERE deleted_at = 0
	GROUP BY date
	ORDER BY date ASC
	`)

	return db.Migrator().CreateView(GetChatMessageStatsViewName(),
		gorm.ViewOption{Replace: true, Query: q})
}
