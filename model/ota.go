package model

type OTAChannel int

const (
	OTAChannelStable OTAChannel = iota
	OTAChannelPreview
)

type OTA struct {
	Model
	Version     string     `json:"version" cosy:"add:required;update:omitempty;list:fussy" gorm:"index"`
	URL         string     `json:"url" cosy:"add:required;update:omitempty"`
	ReleaseNote string     `json:"release_note,omitempty" cosy:"add:required;update:omitempty"`
	Channel     OTAChannel `json:"channel" cosy:"all:omitempty" gorm:"default:0;index"`
}
