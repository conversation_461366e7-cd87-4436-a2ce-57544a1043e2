package model

import "gorm.io/gorm"

const (
	TypePost = "post"
	TypePage = "page"
)

const (
	ContentTypeLink     = "link"
	ContentTypeRichtext = "richtext"
)

const (
	PostStatusDraft   = "draft"
	PostStatusPublish = "publish"
	PostStatusPrivate = "private"
)

type Post struct {
	Model          `json:",squash"`
	Title          string        `json:"title" cosy:"add:required;update:omitempty;list:fussy"`
	Content        string        `json:"content,omitempty" cosy:"all:omitempty" gorm:"type:longtext"`
	PostCategoryId uint64        `json:"post_category_id,string,omitempty" cosy:"all:omitempty;list:eq"`
	PostCategory   *PostCategory `json:"post_category,omitempty" cosy:"list:preload;item:preload"`
	Type           string        `json:"type" cosy:"add:required,oneof=post page;update:omitempty,oneof=post page;list:eq" gorm:"index"`
	UserId         uint64        `json:"user_id"`
	User           *User         `json:"user,omitempty"`
	BannerId       uint64        `json:"banner_id,omitempty,string" cosy:"all:omitempty"`
	Banner         *Upload       `json:"banner,omitempty" cosy:"item:preload"`
	Alias          string        `json:"alias" cosy:"all:omitempty;list:fussy"`
	Keywords       string        `json:"keywords" cosy:"all:omitempty"`
	ContentType    string        `json:"content_type" cosy:"add:required,oneof=link richtext product;update:omitempty,oneof=link richtext product;list:eq" gorm:"default:richtext"`
	Abstract       string        `json:"abstract" cosy:"all:omitempty" gorm:"type:longtext"`
	Status         string        `json:"status" cosy:"add:required,oneof=draft publish private;update:omitempty,oneof=draft publish private;list:eq" gorm:"default:draft;index"`
}

func (p *Post) AfterSave(tx *gorm.DB) (err error) {
	DropCache("post", p.ID)
	return nil
}

func (p *Post) AfterDelete(tx *gorm.DB) (err error) {
	DropCache("post", p.ID)
	return nil
}
