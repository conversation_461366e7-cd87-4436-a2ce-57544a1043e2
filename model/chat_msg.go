package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

type ChatMessageRole string

const (
	ChatMessageRoleUser      ChatMessageRole = "user"
	ChatMessageRoleAssistant ChatMessageRole = "assistant"
	ChatMessageRoleTool      ChatMessageRole = "tool"
)

type ChatMessage struct {
	Model
	UserID           uint64          `json:"user_id,string" gorm:"index"`
	User             *User           `json:"user,omitempty"`
	ChatID           uint64          `json:"chat_id,string" gorm:"index"`
	Chat             *Chat           `json:"chat,omitempty"`
	Role             ChatMessageRole `json:"role" gorm:"index"`
	Content          string          `json:"content"`
	Embedding        string          `json:"embedding"`
	AsrURL           string          `json:"asr_url"` //用户一次对话的声音
	Name             string          `json:"name" gorm:"index"`
	LLM              string          `json:"llm" gorm:"index"`
	PromptTokens     uint            `json:"prompt_tokens" gorm:"column:prompt_tokens"`
	CompletionTokens uint            `json:"completion_tokens" gorm:"column:completion_tokens"`
	TotalTokens      uint            `json:"total_tokens" gorm:"column:total_tokens"`
	PromptMS         uint            `json:"prompt_ms" gorm:"column:prompt_ms"`
	CompletionMS     uint            `json:"completion_ms" gorm:"column:completion_ms"`
	TotalMS          uint            `json:"total_ms" gorm:"column:total_ms"`
	Length           uint            `json:"length"` //音频长度
	States           *DeviceState    `json:"states" gorm:"type:longtext;serializer:json"`
	ToolCalls        string          `json:"tool_calls"`
	ToolCallId       string          `json:"tool_call_id"`
}

func (c *ChatMessage) BeforeCreate(_ *gorm.DB) (err error) {
	if c.ID == 0 {
		c.ID = sonyflake.NextID()
	}
	return
}

type DeviceState struct {
	Volume   float64 `json:"volume"`
	Level    float64 `json:"level"`
	Charging bool    `json:"charging"`
}
