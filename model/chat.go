package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

type Chat struct {
	Model
	UserID    uint64            `json:"user_id,string" cosy:"add:required;update:omitempty" gorm:"index"`
	User      *User             `json:"user,omitempty" cosy:"add:required;update:omitempty"`
	AgentID   uint64            `json:"agent_id,string" cosy:"add:required" gorm:"index"`
	Agent     *IntelligentAgent `json:"agent,omitempty"`
	DeviceID  uint64            `json:"device_id,string" cosy:"add:required" gorm:"index"`
	Device    *Device           `json:"device,omitempty"`
	SessionID string            `json:"session_id" gorm:"index"`
	Title     string            `json:"title"`
	Messages  []*ChatMessage    `json:"messages,omitempty"`
}

func (c *Chat) BeforeCreate(tx *gorm.DB) error {
	if c.ID == 0 {
		c.ID = sonyflake.NextID()
	}
	return nil
}
