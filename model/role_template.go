package model

import (
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// RoleTemplate 角色模板表
type RoleTemplate struct {
	Model
	Name               string          `json:"name" cosy:"add:required;update:omitempty;list:fussy" gorm:"type:varchar(32);index"` // 角色模版名称
	Description        string          `json:"description" cosy:"add:required;update:omitempty"`                                   // 角色模版描述
	ImgID              uint64          `json:"img_id,string" cosy:"all:omitempty"`                                                 // 角色模版图片 ID
	Img                *Upload         `json:"img,omitempty" cosy:"list:preload;item:preload"`                                     // 角色模版图片
	VoiceType          string          `json:"voice_type" cosy:"all:omitempty" gorm:"index"`                                       // 音色选择（角色声音）
	Prompt             string          `json:"prompt" cosy:"all:omitempty"`                                                        // 角色性格（角色介绍，提示词，prompt）
	Language           string          `json:"language" cosy:"all:omitempty;list:fussy" gorm:"index"`                              // 语言偏好
	RoleCategoryIDs    []string        `json:"role_category_ids" cosy:"all:omitempty" gorm:"serializer:json;type:json"`            // 角色分类 IDs
	RoleCategories     []*RoleCategory `json:"role_categories,omitempty" gorm:"-"`                                                 // 角色分类
	Preset             bool            `json:"preset" cosy:"all:omitempty;list:eq" gorm:"index"`                                   // 是否为预设角色
// preset: true 时，是官方预设，
}

func (r *RoleTemplate) PreloadRoleCategories() {
	if len(r.RoleCategoryIDs) == 0 {
		return
	}
	db.Model(&RoleCategory{}).Where("id IN (?)", r.RoleCategoryIDs).Find(&r.RoleCategories)
}

type RoleTemplateList []*RoleTemplate

func (r *RoleTemplateList) PreloadRoleCategories() {
	ids := []string{}
	for _, roleCategory := range *r {
		ids = append(ids, roleCategory.RoleCategoryIDs...)
	}

	ids = lo.Uniq(ids)
	if len(ids) == 0 {
		return
	}
	var roleCategories []*RoleCategory
	db.Model(&RoleCategory{}).Where("id IN (?)", ids).Find(&roleCategories)
	var roleCategoryMap = make(map[string]*RoleCategory)
	for _, roleCategory := range roleCategories {
		roleCategoryMap[cast.ToString(roleCategory.ID)] = roleCategory
	}

	for _, roleTemplate := range *r {
		roleTemplate.RoleCategories = lo.Map(roleTemplate.RoleCategoryIDs, func(id string, _ int) *RoleCategory {
			return roleCategoryMap[id]
		})
	}
}
