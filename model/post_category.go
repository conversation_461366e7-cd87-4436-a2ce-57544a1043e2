package model

import "gorm.io/gorm"

type PostCategory struct {
	Model
	Name        string  `json:"name" cosy:"add:required;update:omitempty"`
	Description string  `json:"description" cosy:"add:required;update:omitempty"`
	BannerID    uint64  `json:"banner_id,string" cosy:"all:omitempty"`
	Banner      *Upload `json:"banner,omitempty" cosy:"list:preload;item:preload"`
}

func (p *PostCategory) AfterSave(tx *gorm.DB) (err error) {
	DropCache("post_category", p.ID)
	return nil
}

func (p *PostCategory) AfterDelete(tx *gorm.DB) (err error) {
	DropCache("post_category", p.ID)
	return nil
}
