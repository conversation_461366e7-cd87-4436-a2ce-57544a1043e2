# potato-api

## 项目文件及目录说明
- main.go

  项目入口


- api
	- admin

	  管理员接口

	- frontend

	   普通用户接口

	- global

	  无权限访问限制的接口的控制器在这里写

	- device

		硬件通信接口

	- api.go

	  框架附带常用函数


- cmd/generate/generate.go

  gen 代码生成

  修改 model 文件夹内的模型后，请执行下面的命令重新生成 query，该命令会自动调用根目录下的 app.ini 配置文件，**使用前注意数据库可以正常连接。**

```bash
./gen.sh
```

- internal

  内部引用的库写这里，下面介绍框架自带的几个包

	- acl

      api鉴权，api权限管理

    - audit

      日志审计，借助阿里云日志服务（SLS）来记录 HTTP 请求和响应的详细信息。

    - geoip

      根据给定的 IP 地址解析出对应的国家 ISO 代码。

      使用了 geoip2-golang 库和 MaxMind 的 GeoLite2 国家数据库文件 GeoLite2 - Country.mmdb 来实现 IP 地址到国家信息的映射。

	- helper

	  辅助函数统一写这里，自带一个 unique.go，创建一个唯一 []int 的数组。

	- limiter

      初始化并提供一个速率限制器（Rate Limiter）实例。

      速率限制器用于控制对某些资源的访问频率，防止过度请求。

      代码借助 rate-limiter-go 库和 Redis 客户端来实现速率限制功能。

    - mail

      封装邮件发送。

    - oss

	  初始化一个 oss 对象，用于上传文件到阿里云 OSS 中。

    - runtime_settings

      对运行时认证设置的管理功能。

    - user

      用户相关基本接口。


- model

  数据模型定义


- query

  gen 生成代码存放处，不要修改里面的内容


- router

  路由定义

	- router.go

	  路由注册

	- middleware.go

	  路由中间件存放


- settings

  系统设置定义


- .air.toml

  air 配置文件


- .drone.yml

  Drone CI 配置文件


- app.example.ini (app.ini)

  系统设置文件


- Dockerfile

  Docker 容器配置文件

- 单元测试
```bash
./test.sh
```
