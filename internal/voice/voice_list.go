package voice

import (
	"embed"
	"encoding/json"
	"io"
	"log"
)

type VoiceType struct {
	Name      string `json:"name"`
	DemoLink  string `json:"demo_link"`
	VoiceType string `json:"voice_type"`
	Language  string `json:"language"`
}

var voiceList = make([]VoiceType, 0)

//go:embed *.json
var fs embed.FS

type VoicePackDetails struct {
	DemoLink  string `json:"demo_link"`
	Language  string `json:"language"`
	VoiceType string `json:"voice_type"`
}

type VoicePack struct {
	ResourceDisplay string           `json:"resource_display"`
	Details         VoicePackDetails `json:"details"`
}

type VoiceTypeResponse struct {
	Result struct {
		Packs []VoicePack `json:"Packs"`
	} `json:"Result"`
}

func init() {
	files, err := fs.ReadDir(".")
	if err != nil {
		log.Fatal(err)
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		jsonFile, err := fs.Open(file.Name())
		if err != nil {
			log.Fatal(err)
		}

		byteValue, err := io.ReadAll(jsonFile)
		if err != nil {
			log.Fatal(err)
		}

		var voiceTypeResponse VoiceTypeResponse
		err = json.Unmarshal(byteValue, &voiceTypeResponse)
		if err != nil {
			log.Fatal(err)
		}

		for _, pack := range voiceTypeResponse.Result.Packs {
			voiceList = append(voiceList, VoiceType{
				Name:      pack.ResourceDisplay,
				DemoLink:  pack.Details.DemoLink,
				VoiceType: pack.Details.VoiceType,
				Language:  pack.Details.Language,
			})
		}
	}
}

func GetVoiceList() []VoiceType {
	return voiceList
}
