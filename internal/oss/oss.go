package oss

import (
	"errors"
	"mime/multipart"
	"os"
	"path"
	"path/filepath"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/settings"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gabriel-vasile/mimetype"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/spf13/cast"
)

type OSS struct {
	Client *oss.Client
	Bucket *oss.Bucket
}

type UploadResult struct {
	Url      string `json:"url"`
	Size     int64  `json:"size"`
	MIME     string `json:"mime"`
	Filename string `json:"filename"`
}

var (
	ErrClientUploadFileType = errors.New("only accept jpeg, png, gif and webp image")
)

func NewOSS() (*OSS, error) {
	client, err := oss.New(settings.OssSettings.EndPoint,
		settings.OssSettings.AccessKeyId, settings.OssSettings.AccessKeySecret)

	if err != nil {
		return nil, err
	}
	var bucket *oss.Bucket

	bucket, err = client.Bucket(settings.OssSettings.BucketName)
	if err != nil {
		return nil, err
	}

	return &OSS{
		Client: client,
		Bucket: bucket,
	}, nil
}

func (o *OSS) PutObject(localPath, ossPath string) error {
	return o.Bucket.PutObjectFromFile(ossPath, localPath)
}

func (o *OSS) GetObjectToFile(ossPath, localPath string, options ...oss.Option) error {
	return o.Bucket.GetObjectToFile(ossPath, localPath, options...)
}

func (o *OSS) DeleteObject(ossPath string) error {
	return o.Bucket.DeleteObject(ossPath)
}

func (o *OSS) CopyObject(src, dst string) error {
	_, err := o.Bucket.CopyObject(src, dst)
	return err
}

func (o *OSS) IsObjectExist(ossPath string) (bool, error) {
	return o.Bucket.IsObjectExist(ossPath)
}

func (o *OSS) UploadSingleFile(c *gin.Context, ossDir string) (res UploadResult, err error) {
	var file *multipart.FileHeader
	file, err = c.FormFile("file")
	if err != nil {
		return
	}

	// keep filename?
	keepFileName := cast.ToBool(c.PostForm("keep_name"))

	res.Filename = filepath.Base(file.Filename)
	res.Size = file.Size

	user := api.CurrentUser(c)

	// 10MB limit for client users
	if user.UserGroupID == 0 && file.Size > 10*1024*1024 {
		err = errors.New("file size exceeds the limit of 10MB")
		return
	}

	// get extension
	ext := filepath.Ext(file.Filename)

	// build a temporary directory
	tempDir, err := os.MkdirTemp("", "")
	if err != nil {
		return
	}

	defer os.RemoveAll(tempDir)

	localPath := path.Join(tempDir, file.Filename)

	var ossPath string

	if keepFileName {
		ossPath = path.Join(ossDir, uuid.New().String(), file.Filename)
	} else {
		ossPath = path.Join(ossDir, uuid.New().String()+ext)
	}

	err = c.SaveUploadedFile(file, localPath)
	if err != nil {
		return
	}

	mtype, err := mimetype.DetectFile(localPath)
	if err != nil {
		res.MIME = "application/octet-stream"
	} else {
		res.MIME = mtype.String()
	}

	err = o.PutObject(localPath, ossPath)
	if err != nil {
		return
	}

	// delete the local file
	err = os.Remove(localPath)
	if err != nil {
		return
	}

	res.Url = ossPath

	return
}
