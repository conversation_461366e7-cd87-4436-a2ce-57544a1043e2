package constant

// 硬件设备状态码定义
// 遵循分层设计原则，便于硬件端识别和处理

// ============= 1xxx 信息性状态码 =============
var ()

// ============= 2xxx 成功状态码 =============
var (
	StatusConnected = 2200 // 连接成功
)

// ============= 3xxx 重定向状态码 =============
var ()

// ============= 4xxx 客户端错误 =============
var (
	StatusRoleNotFound = 4404 // 未绑定角色
)

// ============= 5xxx 服务器错误 =============
var (
	// 基础服务器错误
	StatusInternalError = 5000 // 内部服务器错误
)

// ============= 6xxx 硬件特定错误 =============
var ()

// ============= 7xxx 协议错误 =============
var ()

// ============= 8xxx 安全相关错误 =============
var ()

// ============= 9xxx 自定义业务错误 =============
var ()
