package wechat

import (
	"crypto/tls"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-api/settings"
	"github.com/uozi-tech/cosy/redis"
)

const (
	tokenApiUrl         = "https://api.weixin.qq.com/cgi-bin/token"
	accessTokenRedisKey = "wechat.access_token"
)

type TokenResp struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

var mutex = &sync.Mutex{}

func GetAccessToken() (string, error) {
	mutex.Lock()
	defer mutex.Unlock()
	accessToken, err := redis.Get(accessTokenRedisKey)
	if err != nil {
		return getAccessToken()
	}
	return accessToken, nil
}

func getAccessToken() (string, error) {
	u, err := url.Parse(tokenApiUrl)
	if err != nil {
		return "", err
	}

	q := u.Query()
	q.Add("grant_type", "client_credential")
	q.Add("appid", settings.WeChatSettings.AppID)
	q.Add("secret", settings.WeChatSettings.AppSecret)

	u.RawQuery = q.Encode()

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		return "", err
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", err
	}

	bytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	tokenResp := TokenResp{}

	err = json.Unmarshal(bytes, &tokenResp)

	if err != nil {
		return "", err
	}

	err = redis.Set(accessTokenRedisKey, tokenResp.AccessToken, time.Duration(tokenResp.ExpiresIn-200)*time.Second)

	if err != nil {
		return "", err
	}

	return tokenResp.AccessToken, nil
}
