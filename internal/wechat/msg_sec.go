package wechat

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

type MsgSecCheckScene int

type MsgSecLabel int

type MsgSecSuggest string

const (
	MsgSecCheckUrl                                 = "https://api.weixin.qq.com/wxa/msg_sec_check"
	MsgSecCheckSceneInfo          MsgSecCheckScene = 1
	MsgSecCheckSceneComment       MsgSecCheckScene = 2
	MsgSecCheckSceneBBS           MsgSecCheckScene = 3
	MsgSecCheckSceneSocialJournal MsgSecCheckScene = 4
	MsgSecCheckVersion                             = "2"
	MsgSecNormalLabel             MsgSecLabel      = 100
	MsgSecAdLabel                 MsgSecLabel      = 10001
	MsgSecPoliticsLabel           MsgSecLabel      = 20001
	MsgSecPornLabel               MsgSecLabel      = 20002
	MsgSecAbuseLabel              MsgSecLabel      = 20003
	MsgSecCrimeLabel              MsgSecLabel      = 20006
	MsgSecCheatingLabel           MsgSecLabel      = 20008
	MsgSecVulgarLabel             MsgSecLabel      = 20012
	MsgSecCopyrightLabel          MsgSecLabel      = 20013
	MsgSecOtherLabel              MsgSecLabel      = 21000
	MsgSecSuggestPass             MsgSecSuggest    = "pass"
	MsgSecSuggestRisky            MsgSecSuggest    = "risky"
	MsgSecSuggestReview           MsgSecSuggest    = "review"
)

type MsgSecResponseResult struct {
	Suggest MsgSecSuggest `json:"suggest"`
	Label   MsgSecLabel   `json:"label"`
}

type MsgSecResponse struct {
	ErrCode int                  `json:"errcode"`
	Errmsg  string               `json:"errmsg"`
	Result  MsgSecResponseResult `json:"result"`
}

func MsgSecCheck(openId string, scene MsgSecCheckScene, content string) (ok bool, label MsgSecLabel, err error) {
	accessToken, err := GetAccessToken()
	if err != nil {
		return
	}

	var sb strings.Builder
	sb.WriteString(MsgSecCheckUrl)
	sb.WriteString("?access_token=")
	sb.WriteString(accessToken)
	url := sb.String()

	data := map[string]interface{}{
		"openid":  openId,
		"scene":   scene,
		"version": MsgSecCheckVersion,
		"content": content,
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}

	var msgSecResponse MsgSecResponse
	err = json.Unmarshal(body, &msgSecResponse)
	if err != nil {
		return
	}

	if msgSecResponse.ErrCode != 0 {
		return false, msgSecResponse.Result.Label, fmt.Errorf("errcode: %d, errmsg: %s", msgSecResponse.ErrCode, msgSecResponse.Errmsg)
	}

	if msgSecResponse.Result.Suggest != MsgSecSuggestPass {
		return false, msgSecResponse.Result.Label, ErrContentIsNotSafe
	}

	return true, msgSecResponse.Result.Label, nil
}
