package wechat

import (
	"bytes"
	"encoding/json"
	"github.com/uozi-tech/cosy"
	"io"
	"net/http"
	"strings"
)

const (
	GetPhoneNumberUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber"
)

type PhoneNumberResponse struct {
	Errcode   int       `json:"errcode"`
	Errmsg    string    `json:"errmsg"`
	PhoneInfo PhoneInfo `json:"phone_info"`
}

type PhoneInfo struct {
	PhoneNumber     string    `json:"phoneNumber"`
	PurePhoneNumber string    `json:"purePhoneNumber"`
	CountryCode     string    `json:"countryCode"`
	Watermark       Watermark `json:"watermark"`
}

type Watermark struct {
	Timestamp int    `json:"timestamp"`
	Appid     string `json:"appid"`
}

func GetPhoneNumber(code string) (string, error) {
	accessToken, err := GetAccessToken()
	if err != nil {
		return "", err
	}

	var sb strings.Builder
	sb.WriteString(GetPhoneNumberUrl)
	sb.WriteString("?access_token=")
	sb.WriteString(accessToken)
	url := sb.String()

	data := map[string]string{
		"code": code,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var phoneNumberResponse PhoneNumberResponse
	err = json.Unmarshal(body, &phoneNumberResponse)
	if err != nil {
		return "", err
	}

	switch phoneNumberResponse.Errcode {
	case -1, 40029, 45011, 40013:
		return "", cosy.NewError(406, phoneNumberResponse.Errmsg)
	}

	return phoneNumberResponse.PhoneInfo.PhoneNumber, nil
}
