package device

import (
	"strconv"
	"strings"

	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
)

func GetUserVoiceprint(userid uint64) []*proto.Voiceprint {
	var Voiceprints []*proto.Voiceprint
	var float_array []float32
	var err error
	voiceprint, err := query.Voiceprint.Where(query.Voiceprint.UserID.Eq(userid)).Find()
	if err != nil {
		return Voiceprints
	} else {
		for _, v := range voiceprint {
			float_array, err = stringToFloatSlice(v.Embedding, ",")
			if err != nil {
				logger.Error("stringToFloatSlice error:", err)
				float_array = []float32{}
				// return Voiceprints
			}
			voiceprint := &proto.Voiceprint{
				Id:              cast.ToString(v.ID),
				SpeakerName:     v.Name,
				SpeakerNickname: v.Salutation,
				Vector:          float_array,
			}
			Voiceprints = append(Voiceprints, voiceprint)
		}

		return Voiceprints
	}
}

// 将字符串转回 []float32
func stringToFloatSlice(embedding, sep string) ([]float32, error) {
	// 清理输入字符串
	cleaned := strings.ReplaceAll(embedding, "[", "")
	cleaned = strings.ReplaceAll(cleaned, "]", "")
	cleaned = strings.ReplaceAll(cleaned, " ", "")

	strs := strings.Split(cleaned, sep)

	var float_embedding_array []float32

	for i, str := range strs {
		if str == "" { // 处理空字符串
			continue
		}
		f, err := strconv.ParseFloat(str, 64)
		if err != nil {
			logger.Error("解析错误: 字符串 '%s' 位置 %d 转换失败: %v", str, i, err)
			return nil, err
		}
		float_embedding_array = append(float_embedding_array, float32(f))
	}
	return float_embedding_array, nil
}
