package device

import (
	"math"
)

// Resample 对输入数据进行重采样
// 输入:
//   - data: 原始数据切片
//   - num: 重采样后的数据点数
//
// 输出:
//   - 重采样后的数据切片
func Resample(data []float64, num int) []float64 {
	if len(data) == 0 || num <= 0 {
		return nil
	}

	// 如果重采样点数等于原始数据点数，直接返回原始数据
	if num == len(data) {
		result := make([]float64, len(data))
		copy(result, data)
		return result
	}

	result := make([]float64, num)
	// 计算采样间隔
	step := float64(len(data)-1) / float64(num-1)

	for i := 0; i < num; i++ {
		// 计算当前点在原始数据中的位置
		pos := float64(i) * step
		// 获取左右两个点的索引
		left := int(math.Floor(pos))
		right := int(math.Ceil(pos))

		// 如果是整数位置，直接使用该点的值
		if left == right {
			result[i] = data[left]
			continue
		}

		// 线性插值
		frac := pos - float64(left)
		result[i] = data[left]*(1-frac) + data[right]*frac
	}

	return result
}

// ResampleInt 对整数类型的数据进行重采样
func ResampleInt(data []int, num int) []int {
	if len(data) == 0 || num <= 0 {
		return nil
	}

	// 将整数数据转换为浮点数
	floatData := make([]float64, len(data))
	for i, v := range data {
		floatData[i] = float64(v)
	}

	// 进行浮点数重采样
	floatResult := Resample(floatData, num)

	// 将结果转换回整数
	result := make([]int, num)
	for i, v := range floatResult {
		result[i] = int(math.Round(v))
	}

	return result
}
