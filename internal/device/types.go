package device

import (
	"sync"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/gorilla/websocket"
)

type WebSocketFrame struct {
	Conn        *websocket.Conn
	mutex       sync.Mutex
	MessageType int    // WebSocketFrameType
	Message     []byte // WebSocketFrameRawMessage
	Data        *WebSocketMessagePayload
	Context     *WebSocketMessagePayload // 握手消息
}

type AudioParams struct {
	Format        string `json:"format,omitempty"`
	SampleRate    int    `json:"sample_rate,omitempty"`
	Channels      int    `json:"channels,omitempty"`
	FrameDuration int    `json:"frame_duration,omitempty"`
}

type DeviceInfo struct {
	*model.Device
	Identifier    string `json:"identifier"`
	SystemVersion string `json:"system_version"`
	Register      bool   `json:"register"`
}

type WebSocketMessagePayload struct {
	Type        string       `json:"type"`
	Version     int          `json:"version,omitempty"`
	Transport   string       `json:"transport,omitempty"`
	AudioParams *AudioParams `json:"audio_params,omitempty"`
	DeviceInfo  *DeviceInfo  `json:"device_info,omitempty"`
	SessionID   string       `json:"session_id,omitempty"`
	State       string       `json:"state,omitempty"`
	Mode        string       `json:"mode,omitempty"`
	Reason      string       `json:"reason,omitempty"`
	Text        string       `json:"text,omitempty"`
	Emotion     string       `json:"emotion,omitempty"`
	Commands    []*Command   `json:"commands,omitempty"`
	Time        int64        `json:"time,omitempty"`
	OTA         *model.OTA   `json:"ota,omitempty"`
	States      []*States    `json:"states,omitempty"`
	Update      bool         `json:"update"`
	StatusCode  int          `json:"status_code,omitempty"`
}

type Command struct {
	Name       string                 `json:"name"`       // 命令名称（如 "Speaker"）
	Method     string                 `json:"method"`     // 方法名（如 "SetVolume"）
	Parameters map[string]interface{} `json:"parameters"` // 原始参数（可后续解析）
	// 或使用具体结构体：
	// Parameters VolumeParameters `json:"parameters"`
}

type States struct {
	Name  string                 `json:"name"`
	State map[string]interface{} `json:"state"` // 修改此处
}

type State struct {
	Volume   int         `json:"volume"`
	Level    int         `json:"level"`
	Charging bool        `json:"charging"`
	Value    interface{} `json:"value"` // 假设存储实际值
}
