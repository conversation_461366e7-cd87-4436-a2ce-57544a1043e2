# Device 包

Device 包提供了通过 WebSocket 管理设备连接和通信的功能

## 概述

该包实现了一个基于 WebSocket 的物联网设备通信协议

## 核心组件

### WebSocket 框架

该包提供了一个 WebSocket 帧系统，用于处理与连接设备的通信：

- `WebSocketFrame`：封装了一个 WebSocket 连接，提供读写消息的方法
- 在 JSON 和 Go 结构体之间进行消息序列化/反序列化
- 通过互斥锁保护的线程安全通信

### 消息类型和处理器注册系统

该包使用基于类型的消息路由系统，处理不同类型的设备消息：

#### 处理器注册机制

- `RegisterHandler(typeName string, handler Handler)`：注册一个处理器函数，用于处理特定类型的消息
- `GetHandler(typeName string) (Handler, bool)`：根据消息类型获取对应的处理器函数
- 处理器函数类型为 `func(*WebSocketFrame)`，接收一个 WebSocket 帧作为参数

#### 处理器注册方式

处理器建议通过以下方式注册：

```go
func init() {
	RegisterHandler("message-type", HandlerFunction)
}
```

### 消息载荷

`WebSocketMessagePayload` 结构体表示标准化的消息格式

## 数据类型

### WebSocketFrame

WebSocketFrame 是设备通信的核心结构，封装了 WebSocket 连接及相关状态。它包含以下字段：

```go
type WebSocketFrame struct {
	Conn        *websocket.Conn
	mutex       sync.Mutex
	MessageType int
	Message     []byte
	Data        *WebSocketMessagePayload
	Context     *WebSocketMessagePayload
}
```

#### 字段说明

- **Conn** `*websocket.Conn`
  - 底层的 gorilla/websocket 连接对象
  - 用于实际的网络通信
  - 通常不应直接操作此连接，而应使用 WebSocketFrame 提供的方法

- **mutex** `sync.Mutex`
  - 用于保护并发写操作的互斥锁
  - 确保多个 goroutine 同时对同一连接写入时的线程安全
  - 在 WriteMessage 和 WriteJSON 方法中自动使用

- **MessageType** `int`
  - 当前接收到的 WebSocket 消息类型
  - 参考 gorilla/websocket 的常量定义（TextMessage、BinaryMessage 等）
  - 用于区分文本消息和二进制消息

- **Message** `[]byte`
  - 原始消息的字节数组
  - 包含从 WebSocket 连接接收到的未解析数据
  - 可能是 JSON 格式的文本或二进制数据

- **Data** `*WebSocketMessagePayload`
  - 当前处理的消息载荷
  - 从原始 JSON 消息解析得到的结构化数据
  - 包含消息类型、设备信息、音频参数等
  - 处理器函数主要通过此字段访问消息内容

- **Context** `*WebSocketMessagePayload`
  - 握手消息的上下文信息
  - 通常在连接建立时存储握手消息
  - 在整个会话期间保留，可用于后续消息处理
  - 常用于存储设备信息、会话 ID 等持久性数据

这些字段共同构成了一个完整的 WebSocket 会话环境，使处理器函数能够访问连接、消息内容和上下文信息。

### DeviceInfo

包含设备元数据：
- 型号信息
- 版本跟踪
- 注册状态

### AudioParams

为带音频功能的设备指定音频配置：
- 格式规范
- 采样率配置
- 通道设置
- 帧持续时间参数

## WebSocketFrame 通信方法

WebSocketFrame 提供了多种方法用于与设备进行通信，这些方法都已实现了线程安全保护。

### WriteMessage 方法

`WriteMessage` 方法用于向设备发送原始消息数据。

#### 函数签名

```go
func (f *WebSocketFrame) WriteMessage(messageType int, message []byte) error
```

#### 参数说明

- `messageType int`：WebSocket 消息类型，常见值包括：
  - `websocket.TextMessage` (1)：文本消息，用于发送 UTF-8 编码的文本
  - `websocket.BinaryMessage` (2)：二进制消息，用于发送原始二进制数据
  - `websocket.CloseMessage` (8)：关闭消息，用于关闭连接
  - `websocket.PingMessage` (9)：Ping 消息
  - `websocket.PongMessage` (10)：Pong 消息

- `message []byte`：要发送的消息内容（字节数组）

#### 返回值

- `error`：如果发送成功，返回 nil；如果发送失败，返回相应的错误

#### 线程安全

此方法使用互斥锁保护，在多个 goroutine 并发调用时是安全的。

#### 使用示例

```go
import (
    "github.com/gorilla/websocket"
    "github.com/uozi-tech/cosy/logger"
    "git.uozi.org/uozi/potato-api/internal/helper"
)

// 发送文本消息
err := frame.WriteMessage(websocket.TextMessage, []byte("Hello Device"))
if err != nil {
    // 使用 helper.IsUnexpectedWebsocketError 忽略一些预期内的错误（如连接关闭）
    if !helper.IsUnexpectedWebsocketError(err) {
        // 只记录非预期的错误
        logger.Error("发送消息失败：" + err.Error())
    }
    return
}

// 发送二进制数据
data := []byte{0x01, 0x02, 0x03}
err = frame.WriteMessage(websocket.BinaryMessage, data)
if err != nil {
    if !helper.IsUnexpectedWebsocketError(err) {
        logger.Error("发送二进制数据失败：" + err.Error())
    }
    return
}
```

### WriteJSON 方法

`WriteJSON` 方法用于将 Go 结构体序列化为 JSON 并发送给设备。

#### 函数签名

```go
func (f *WebSocketFrame) WriteJSON(v any) error
```

#### 参数说明

- `v any`：要发送的数据结构，将被序列化为 JSON 格式

#### 返回值

- `error`：如果序列化或发送成功，返回 nil；失败则返回相应错误

#### 线程安全

此方法使用互斥锁保护，在多个 goroutine 并发调用时是安全的。

#### 使用示例

```go
import (
    "time"
    "github.com/uozi-tech/cosy/logger"
    "git.uozi.org/uozi/potato-api/internal/helper"
)

response := &WebSocketMessagePayload{
    Type: "response",
    Time: time.Now().Unix(),
}

err := frame.WriteJSON(response)
if err != nil {
    if !helper.IsUnexpectedWebsocketError(err) {
        logger.Error("发送 JSON 消息失败：" + err.Error() + "，类型：" + response.Type)
    }
    return
}
```

### ReadMessage 方法

`ReadMessage` 方法用于从 WebSocket 连接读取消息。

#### 函数签名

```go
func (f *WebSocketFrame) ReadMessage() (messageType int, message []byte, err error)
```

#### 返回值

- `messageType int`：接收到的 WebSocket 消息类型
- `message []byte`：接收到的消息内容（字节数组）
- `err error`：如果读取成功，返回 nil；失败则返回相应错误

#### 线程安全

此方法不提供互斥锁保护，应确保每个 WebSocket 连接只有一个 goroutine 调用此方法。

## 使用示例

### 创建和注册处理器

```go
package device

import (
    "github.com/uozi-tech/cosy/logger"
    "git.uozi.org/uozi/potato-api/internal/device"
    "git.uozi.org/uozi/potato-api/internal/helper"
)

// 在包初始化时注册处理器
func init() {
    device.RegisterHandler("my-message-type", MyHandler)
}

// 处理器函数实现
func MyHandler(f *device.WebSocketFrame) {
    // 处理消息
    // 通过 f.Data 访问载荷

    // 记录接收到的消息
    logger.Info("收到消息，类型：" + f.Data.Type)

    // 发送响应
    err := f.WriteJSON(&device.WebSocketMessagePayload{
        Type: "response-type",
        // 其他字段...
    })

    if err != nil {
        if !helper.IsUnexpectedWebsocketError(err) {
            logger.Error("响应发送失败：" + err.Error())
        }
    }
}
```
## 线程安全

只有 `WebSocketFrame` 中的 `WriteMessage` 和 `WriteJSON` 提供互斥锁保护。
