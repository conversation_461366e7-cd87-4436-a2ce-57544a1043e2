package device

import (
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/uozi-tech/cosy/logger"
)

func init() {
	RegisterHandler("ota", OTA)
}

func OTA(f *WebSocketFrame) {
	device := f.Context.DeviceInfo
	otaChannel := device.OTAChannel
	otaEnabled := device.OTAEnabled

	if !otaEnabled {
		logger.Error("admin端未设置ota升级")
		f.WriteJSON(&WebSocketMessagePayload{
			Type: "ota",
			OTA:  &model.OTA{},
		})
		return
	}

	q := query.OTA
	ota, err := q.Where(q.Channel.Eq(int(otaChannel))).Order(q.CreatedAt.Desc()).First()
	if err != nil {
		f.WriteJSON(&WebSocketMessagePayload{
			Type: "ota",
			OTA:  &model.OTA{},
		})
		logger.Error(err)
		return
	}

	// 隐藏发布日志，节省带宽
	ota.ReleaseNote = ""
	f.WriteJSON(&WebSocketMessagePayload{
		Type: "ota",
		OTA:  ota,
	})
}
