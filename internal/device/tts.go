package device

import "github.com/uozi-tech/cosy/logger"

func init() {
	Register<PERSON>andler("tts_start", TTSStart)
	RegisterHandler("tts_sentence", TTSSentence)
	RegisterHandler("tts_stop", TTSStop)
	RegisterHandler("llm_emotion", LLMEmotion)
	RegisterHandler("audio_params", AudioParamsHandler)
	RegisterHandler("tts_error", TTSError)

}

func TTSStart(f *WebSocketFrame) {
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:  "tts",
		State: "start",
	})
	if err != nil {
		logger.Error("发送tts.start到websocket失败:", err)
		return
	}
}
func TTSSentence(f *WebSocketFrame) {
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:  "tts",
		State: "sentence_start",
		Text:  f.Data.Text,
	})
	if err != nil {
		logger.Error("发送tts.sentence到websocket失败:", err)
		return
	}
}
func TTSStop(f *WebSocketFrame) {
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:  "tts",
		State: "stop",
	})
	if err != nil {
		logger.Error("发送tts.stop到websocket失败:", err)
		return
	}
}
func LLMEmotion(f *WebSocketFrame) {
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:    "llm",
		Emotion: f.Data.Emotion,
		Text:    f.Data.Text,
	})
	if err != nil {
		logger.Error("发送llm.emotion到websocket失败:", err)
		return
	}
}

func AudioParamsHandler(f *WebSocketFrame) {

	// 发送音频数据到WebSocket
	err := f.WriteJSON(WebSocketMessagePayload{
		AudioParams: f.Data.AudioParams,
	})
	if err != nil {
		logger.Error("发送音频流到WebSocket失败:", err)
		return
	}
}

func TTSError(f *WebSocketFrame) {
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:  "tts",
		State: "sentence_start",
		Text:  f.Data.Text,
	})
	if err != nil {
		logger.Error("发送error到websocket失败:", err)
		return
	}
}
