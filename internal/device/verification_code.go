package device

import (
	"os"

	"github.com/skip2/go-qrcode"
	"github.com/uozi-tech/cosy/logger"
)

func init() {
	RegisterHandler("verification_code", VerificationCode)
	RegisterHandler("qrcode", Qrcode)
}

func VerificationCode(f *WebSocketFrame) {

	err := f.Conn.WriteJSON(&WebSocketMessagePayload{
		Type: "verification_code",
		Text: f.Data.Text,
	})
	if err != nil {
		logger.Error("发送验证码失败", err)
	}
}

func Qrcode(f *WebSocketFrame) {
	logger.Debug("生成二维码...")
	err := f.Conn.WriteJSON(&WebSocketMessagePayload{
		Type: "qrcode",
		Text: f.Data.Text,
	})
	if err != nil {
		logger.Error(err)
		return
	}
}

func GetSimpleQrcodePath(code string) (string, error) {
	//保存path
	dir := "statics/images"
	// 确保目录存在
	if err := os.MkdirAll(dir, 0755); err != nil {
		logger.Error("创建目录失败:", err)
		return "", err
	}
	pngpath := dir + "/" + code + ".png"
	//生成二维码
	// 生成一个6位随机验证码
	vercode := code
	qrCode, err := qrcode.New(vercode, qrcode.Highest)
	if err != nil {
		logger.Error(err)
		return "", err
	}
	qrCode.DisableBorder = true
	//保存成文件
	qrCode.WriteFile(256, pngpath)
	logger.Debug("二维码生成结束")

	return pngpath, nil
}
