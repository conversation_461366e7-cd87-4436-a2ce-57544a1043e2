package device

import (
	"time"

	"github.com/uozi-tech/cosy/logger"
)

func init() {
	RegisterHandler("hello", Hello)
}

// Hello 处理 Hello 消息
func Hello(f *WebSocketFrame) {
	if f.Data == nil || f.Data.AudioParams == nil {
		f.Data = &WebSocketMessagePayload{
			AudioParams: &AudioParams{
				SampleRate: 16000,
			},
		}
	}

	err := f.WriteJSON(WebSocketMessagePayload{
		Type:      "hello",
		Transport: "websocket",
		AudioParams: &AudioParams{
			SampleRate: f.Data.AudioParams.SampleRate,
		},
		DeviceInfo: f.Data.DeviceInfo,
		Time:       time.Now().Unix(),
	})
	if err != nil {
		logger.Error(err)
		return
	}
}
