package device

import "github.com/uozi-tech/cosy/logger"

func init() {
	<PERSON><PERSON>and<PERSON>("iot", Iot)
}

func Iot(f *WebSocketFrame) {
	logger.Info("接收到iot消息:", f.Data.Type)
	err := f.WriteJSON(WebSocketMessagePayload{
		Type:      f.Data.Type,
		SessionID: f.Data.SessionID,
		Commands:  f.Data.Commands,
	})
	logger.Info("发送到iot消息:", f.Data.Type)
	if err != nil {
		logger.Error("发送tts.sentence到websocket失败:", err)
		return
	}
}
