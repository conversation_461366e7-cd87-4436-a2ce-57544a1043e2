package device

import (
	"git.uozi.org/uozi/potato-api/internal/helper"
)

// WriteMessage 写入消息
func (f *WebSocketFrame) WriteMessage(messageType int, message []byte) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	err := f.Conn.WriteMessage(messageType, message)
	if err != nil {
		if helper.IsUnexpectedWebsocketError(err) {
			return err
		}
		return err
	}
	return nil
}

// WriteJSON 写入JSON消息
func (f *WebSocketFrame) WriteJSON(v any) error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	err := f.Conn.WriteJSON(v)
	if err != nil {
		if helper.IsUnexpectedWebsocketError(err) {
			return err
		}
		return err
	}
	return nil
}

// ReadMessage 读取消息
func (f *WebSocketFrame) ReadMessage() (messageType int, message []byte, err error) {
	return f.Conn.ReadMessage()
}
