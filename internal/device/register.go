package device

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"math/big"
	"time"

	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

var colorhead = []byte{
	0xFF, 0xFF, 0xFF, 0xFF,
	0x00, 0x00, 0x00, 0xff,
}

// 六位验证码的前缀
const RedisCodePrefix = "six_unique_code:"

func GenCodeRedisKey(key string) string {
	return RedisCodePrefix + key
}
func init() {
	RegisterHandler("register", Register)
}

// Register 注册绑定设备
func Register(f *WebSocketFrame) {
	// 生成验证码
	code, err := GetSixUniqueCode()
	sixcode := cast.ToString(code)
	if err != nil {
		err = f.WriteJSON(&WebSocketMessagePayload{
			Type: "error",
			Text: "code generate error",
		})
		logger.Error(err)
		return
	}
	logger.Debug("Register.Redis.key->", GenCodeRedisKey(sixcode))
	redis.Set(GenCodeRedisKey(sixcode), f.Context.DeviceInfo.MAC, 5*time.Minute)
	f.Data.Text = sixcode
	logger.Debug("设备未注册，返回验证码中...")
	// 返回验证码
	handler, ok := GetHandler("verification_code")
	if ok {
		handler(f)
	} else {
		logger.Error("verification_code处理函数失败")
	}
	// 生成二维码
	qrcodepath, err := GetSimpleQrcodePath(sixcode)
	if err != nil {
		logger.Error("二维码生成失败", err)
	}
	// 处理二维码图片
	imageLvglImage, err := CompressImage(qrcodepath)
	// colorhead := PaletteToBytes(palette)

	imageDatas := append(colorhead, imageLvglImage...)
	// logger.Debug("imageDatas,二维码经过Base64编码后->")
	for i := 0; i < len(imageDatas); i++ {
		fmt.Print(imageDatas[i])
		fmt.Print(",")
		if i%30 == 0 && i != 0 {
			fmt.Println()
		}
	}
	encoded := base64.StdEncoding.EncodeToString(imageDatas)
	f.Data.Text = encoded
	// logger.Debug("f.Data.Text,二维码经过Base64编码后->", f.Data.Text)
	if err != nil {
		logger.Debug(err)
	}
	// 返回二维码
	f.Message = imageLvglImage
	handler, ok = GetHandler("qrcode")
	if ok {
		handler(f)
	} else {
		logger.Error("qrcode处理函数处理失败")
	}
	logger.Debug("返回二维码成功")
}

func GenerateSixUniqueCode() (int, error) {
	// Generate a number between 0 and 899999.
	n, err := rand.Int(rand.Reader, big.NewInt(900000))
	if err != nil {
		return 0, err
	}
	// Ensure the number is at least 100000.
	return int(n.Int64() + 100000), nil
}

func GetSixUniqueCode() (int, error) {
	for {
		code, err := GenerateSixUniqueCode()
		if err != nil {
			return 0, err
		}
		num := cast.ToString(code)
		_, err = redis.Get(GenCodeRedisKey(num))
		if err != nil {
			logger.Debug("noerr_stringnum", num)
			return code, nil
		}
		logger.Debug("err_stringnum", num)
	}
}
