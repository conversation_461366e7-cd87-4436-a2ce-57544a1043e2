package analytics

import (
	"time"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/jinzhu/now"
	"github.com/uozi-tech/cosy"
)

// GetUserCount 获取用户数量
func GetUserCount() (int64, error) {
	db := cosy.UseDB()
	var count int64
	err := db.Model(&model.User{}).Where("deleted_at = 0").Count(&count).Error
	return count, err
}

// GetDailyRegisterStats 获取近14天每日注册用户数
func GetDailyRegisterStats() ([]DailyRegisterStat, error) {
	var stats []DailyRegisterStat

	// 获取14天前的日期
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -13) // 14天包括今天

	// 设置开始日期为当天的开始时间
	startDateTime := now.New(startDate).BeginningOfDay().Unix()
	db := cosy.UseDB()
	// 查询每天的注册用户数
	rows, err := db.Model(&model.User{}).
		Select("FROM_UNIXTIME(created_at, '%Y-%m-%d') as date, COUNT(*) as count").
		Where("created_at >= ? AND deleted_at = 0", startDateTime).
		Group("date").
		Order("date ASC").
		Rows()

	if err != nil {
		return stats, err
	}
	defer rows.Close()

	// 准备一个日期到计数的映射
	dateCountMap := make(map[string]int64)

	// 读取查询结果
	for rows.Next() {
		var date string
		var count int64
		if err := rows.Scan(&date, &count); err != nil {
			return stats, err
		}
		dateCountMap[date] = count
	}

	// 填充每一天的数据，包括没有用户注册的日期
	for i := 0; i < 14; i++ {
		date := startDate.AddDate(0, 0, i)
		dateStr := date.Format(time.DateOnly)
		count, exists := dateCountMap[dateStr]
		if !exists {
			count = 0
		}
		stats = append(stats, DailyRegisterStat{
			Date:  dateStr,
			Count: count,
		})
	}

	return stats, nil
}
