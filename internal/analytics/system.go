package analytics

import (
	"time"
)

// GetSystemStatistics 获取系统相关统计数据
func GetSystemStatistics() (SystemStatistics, error) {
	stats := SystemStatistics{}
	var err error

	// 获取角色模板总量
	stats.RoleTemplateCount, err = GetRoleTemplateCount()
	if err != nil {
		return stats, err
	}

	// 获取 OTA 数量
	stats.OTACount, err = GetOTACount()
	if err != nil {
		return stats, err
	}

	// 获取用户数量
	stats.UserCount, err = GetUserCount()
	if err != nil {
		return stats, err
	}

	// 获取近14天每日注册用户数
	stats.DailyRegister, err = GetDailyRegisterStats()
	if err != nil {
		return stats, err
	}

	// 获取聊天消息统计
	stats.ChatMessageStats, err = GetChatMessageStats()
	if err != nil {
		return stats, err
	}

	// 获取近14天OTA发版统计
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -14)

	otaReleases, err := GetOTAReleases(startDate, endDate)
	if err != nil {
		return stats, err
	}
	// 转换类型
	for _, ota := range otaReleases {
		stats.OTAReleases = append(stats.OTAReleases, OTAReleaseStat{
			Date:  ota.Date,
			Count: ota.Count,
		})
	}

	// 获取近14天角色模板使用统计
	roleUsage, err := GetRoleTemplateUsage(startDate, endDate)
	if err != nil {
		return stats, err
	}
	// 转换类型
	for _, usage := range roleUsage {
		stats.RoleTemplateUsage = append(stats.RoleTemplateUsage, TemplateUsageStat{
			Date:  usage.Date,
			Count: usage.Count,
		})
	}

	return stats, nil
}

// GetChatMessageStatsHandler 处理获取聊天消息统计的请求
func GetChatMessageStatsHandler() ([]ChatMessageStats, error) {
	return GetChatMessageStats()
}
