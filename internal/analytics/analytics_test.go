package analytics

import (
	"testing"
	"time"

	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/model/view"
	"github.com/stretchr/testify/assert"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/sandbox"
)

func TestGetAllStatistics(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		// 注册所有需要的模型
		RegisterModels(model.User{}, model.RoleTemplate{}, model.OTA{}, model.Device{}, model.DeviceType{}, model.ChatMessage{}).
		// 运行测试函数
		Run(func(instance *sandbox.Instance) {
			// 准备测试数据
			prepareTestData(t)

			// 测试 GetAllStatistics 函数
			stats, err := GetAllStatistics()
			assert.NoError(t, err)
			assert.NotNil(t, stats)

			// 验证统计数据正确性
			assert.Equal(t, int64(2), stats.RoleTemplateCount)
			assert.Equal(t, int64(1), stats.OTACount)
			assert.Equal(t, int64(3), stats.UserCount)
			assert.Equal(t, int64(2), stats.DeviceStats.TotalDevices)
			assert.Equal(t, int64(1), stats.DeviceStats.BoundDevices)
			assert.Equal(t, int64(1), stats.DeviceStats.UnboundDevices)
			assert.Equal(t, int64(1), stats.DeviceStats.DeviceTypeCount)

			// 测试聊天设备计数
			assert.Equal(t, int64(0), stats.DeviceStats.ChatingDeviceCount)

			// 添加一个聊天设备
			AddChatingDevice(1)

			// 再次获取统计数据
			stats, err = GetAllStatistics()
			assert.NoError(t, err)
			assert.Equal(t, int64(1), stats.DeviceStats.ChatingDeviceCount)

			// 移除聊天设备
			RemoveChatingDevice(1)

			// 再次获取统计数据
			stats, err = GetAllStatistics()
			assert.NoError(t, err)
			assert.Equal(t, int64(0), stats.DeviceStats.ChatingDeviceCount)

			// 检查每日注册数据
			assert.Len(t, stats.DailyRegister, 14) // 应该有14天的数据

			// 检查第一天的注册数量（根据测试数据应该有1个注册）
			today := time.Now().Format(time.DateOnly)
			found := false
			for _, day := range stats.DailyRegister {
				if day.Date == today {
					assert.Equal(t, int64(3), day.Count) // 今天有3个用户注册
					found = true
					break
				}
			}
			assert.True(t, found, "今天的注册数据应该存在")
		})
}

func TestGetDeviceStatistics(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		RegisterModels(model.Device{}, model.DeviceType{}).
		Run(func(instance *sandbox.Instance) {
			// 准备测试数据
			prepareDeviceTestData(t)

			// 测试 GetDeviceStatistics 函数
			stats, err := GetDeviceStatistics()
			assert.NoError(t, err)
			assert.NotNil(t, stats)

			// 验证设备统计正确
			assert.Equal(t, int64(2), stats.TotalDevices)
			assert.Equal(t, int64(1), stats.BoundDevices)
			assert.Equal(t, int64(1), stats.UnboundDevices)
			assert.Equal(t, int64(1), stats.DeviceTypeCount)
		})
}

func TestGetSystemStatistics(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		RegisterModels(model.User{}, model.RoleTemplate{}, model.OTA{}, model.ChatMessage{}).
		Run(func(instance *sandbox.Instance) {
			// 准备测试数据
			prepareSystemTestData(t)

			// 测试 GetSystemStatistics 函数
			stats, err := GetSystemStatistics()
			assert.NoError(t, err)
			assert.NotNil(t, stats)

			// 验证系统统计正确
			assert.Equal(t, int64(2), stats.RoleTemplateCount)
			assert.Equal(t, int64(1), stats.OTACount)
			assert.Equal(t, int64(3), stats.UserCount)
			assert.Len(t, stats.DailyRegister, 14) // 应该有14天的数据
		})
}

func TestChatingDevice(t *testing.T) {
	sandbox.NewInstance("../../app.testing.ini", "mysql").
		Run(func(instance *sandbox.Instance) {
			// 测试添加聊天设备
			assert.Equal(t, 0, GetChatingDeviceCount())

			// 添加设备
			AddChatingDevice(1)
			assert.Equal(t, 1, GetChatingDeviceCount())

			// 添加另一个设备
			AddChatingDevice(2)
			assert.Equal(t, 2, GetChatingDeviceCount())

			// 移除设备
			RemoveChatingDevice(1)
			assert.Equal(t, 1, GetChatingDeviceCount())

			// 移除不存在的设备不应该报错
			RemoveChatingDevice(99)
			assert.Equal(t, 1, GetChatingDeviceCount())

			// 清空
			RemoveChatingDevice(2)
			assert.Equal(t, 0, GetChatingDeviceCount())
		})
}

// 用于准备测试所需的所有数据
func prepareTestData(t *testing.T) {
	prepareSystemTestData(t)
	prepareDeviceTestData(t)
}

// 准备系统相关测试数据
func prepareSystemTestData(t *testing.T) {
	db := cosy.UseDB()
	view.CreateViews(db)

	// 创建角色模板
	roleTemplates := []model.RoleTemplate{
		{
			Model: model.Model{
				ID: 1,
			},
			Name:        "角色模板1",
			Description: "测试角色模板1",
		},
		{
			Model: model.Model{
				ID: 2,
			},
			Name:        "角色模板2",
			Description: "测试角色模板2",
		},
	}
	for _, rt := range roleTemplates {
		assert.NoError(t, db.Create(&rt).Error)
	}

	// 创建OTA
	ota := model.OTA{
		Model: model.Model{
			ID: 1,
		},
		Version:     "1.0.0",
		URL:         "https://example.com/ota/1.0.0",
		ReleaseNote: "测试OTA",
	}
	assert.NoError(t, db.Create(&ota).Error)

	// 创建用户
	users := []model.User{
		{
			Model: model.Model{
				ID: 1,
			},
			Name:     "用户1",
			Email:    "<EMAIL>",
			Password: "password",
		},
		{
			Model: model.Model{
				ID: 2,
			},
			Name:     "用户2",
			Email:    "<EMAIL>",
			Password: "password",
		},
		{
			Model: model.Model{
				ID: 3,
			},
			Name:     "用户3",
			Email:    "<EMAIL>",
			Password: "password",
		},
	}
	for _, user := range users {
		assert.NoError(t, db.Create(&user).Error)
	}
}

// 准备设备相关测试数据
func prepareDeviceTestData(t *testing.T) {
	db := cosy.UseDB()

	// 创建设备类型
	deviceType := model.DeviceType{
		Model: model.Model{
			ID: 1,
		},
		Name:       "测试设备类型",
		Identifier: "test_device_type",
	}
	assert.NoError(t, db.Create(&deviceType).Error)

	// 创建设备
	devices := []model.Device{
		{
			Model: model.Model{
				ID: 1,
			},
			MAC:        "00:11:22:33:44:55",
			UserId:     1, // 绑定到用户1
			Identifier: "test_device_type",
		},
		{
			Model: model.Model{
				ID: 2,
			},
			MAC:        "55:44:33:22:11:00",
			UserId:     0, // 未绑定
			Identifier: "test_device_type",
		},
	}
	for _, device := range devices {
		assert.NoError(t, db.Create(&device).Error)
	}
}
