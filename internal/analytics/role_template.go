package analytics

import (
	"time"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// GetRoleTemplateCount 获取角色模板总量
func GetRoleTemplateCount() (int64, error) {
	db := cosy.UseDB()
	var count int64
	err := db.Model(&model.RoleTemplate{}).Where("deleted_at = 0").Count(&count).Error
	return count, err
}

// RoleTemplateUsage 表示角色模板使用统计数据
type RoleTemplateUsage struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// GetRoleTemplateUsage 获取指定时间范围内的角色模板使用统计
func GetRoleTemplateUsage(startDate, endDate time.Time) ([]RoleTemplateUsage, error) {
	db := cosy.UseDB()
	var usageData []RoleTemplateUsage

	// 格式化日期为 YYYY-MM-DD 格式
	startDateStr := startDate.Format(time.DateOnly)
	endDateStr := endDate.Format(time.DateOnly)

	stmt := gorm.Statement{DB: db}
	_ = stmt.Parse(&model.Chat{})
	tableName := stmt.Table

	query := `
		SELECT
			DATE_FORMAT(FROM_UNIXTIME(created_at), '%Y-%m-%d') as date,
			COUNT(*) as count
		FROM ` + "`" + tableName + "`" + `
		WHERE
			created_at BETWEEN UNIX_TIMESTAMP(?) AND UNIX_TIMESTAMP(?)
			AND deleted_at = 0
		GROUP BY
			date
		ORDER BY
			date ASC
	`

	err := db.Raw(query, startDateStr, endDateStr).Scan(&usageData).Error
	if err != nil {
		return nil, err
	}

	// 填充没有数据的日期
	result := fillMissingDateForRoleTemplate(startDate, endDate, usageData)

	return result, nil
}

// 填充缺失的日期，确保返回连续的日期数据
func fillMissingDateForRoleTemplate(startDate, endDate time.Time, data []RoleTemplateUsage) []RoleTemplateUsage {
	// 创建日期映射
	dateMap := make(map[string]RoleTemplateUsage)
	for _, item := range data {
		dateMap[item.Date] = item
	}

	// 填充缺失的日期
	var result []RoleTemplateUsage
	currentDate := startDate

	for !currentDate.After(endDate) {
		dateStr := currentDate.Format(time.DateOnly)
		if item, exists := dateMap[dateStr]; exists {
			result = append(result, item)
		} else {
			result = append(result, RoleTemplateUsage{
				Date:  dateStr,
				Count: 0,
			})
		}
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return result
}
