package analytics

import (
	"time"

	"git.uozi.org/uozi/potato-api/model/view"
	"github.com/jinzhu/now"
	"github.com/uozi-tech/cosy"
)

// GetChatMessageStats 获取近14天的聊天消息统计数据
func GetChatMessageStats() ([]ChatMessageStats, error) {
	var stats []ChatMessageStats

	// 获取14天前的日期
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -13) // 14天包括今天

	// 设置开始日期为当天的开始时间
	startDateTime := now.New(startDate).BeginningOfDay().Format(time.DateOnly)
	db := cosy.UseDB()

	// 直接从视图中查询数据
	err := db.Table(view.GetChatMessageStatsViewName()).
		Where("date >= ?", startDateTime).Order("date ASC").Find(&stats).Error
	if err != nil {
		return stats, err
	}

	// 准备一个日期到统计数据的映射
	statsMap := make(map[string]ChatMessageStats)
	for _, stat := range stats {
		statsMap[stat.Date] = stat
	}

	// 填充每一天的数据，包括没有聊天消息的日期
	var result []ChatMessageStats
	for i := 0; i < 14; i++ {
		date := startDate.AddDate(0, 0, i)
		dateStr := date.Format(time.DateOnly)

		if stat, exists := statsMap[dateStr]; exists {
			result = append(result, stat)
		} else {
			// 如果当天没有数据，添加零值
			result = append(result, ChatMessageStats{
				Date:              dateStr,
				MessageCount:      0,
				TotalPromptTokens: 0,
				TotalCompTokens:   0,
				TotalTokens:       0,
				TotalPromptMS:     0,
				TotalCompMS:       0,
				TotalMS:           0,
				AvgPromptMS:       0,
				AvgCompMS:         0,
				AvgMS:             0,
			})
		}
	}

	return result, nil
}

// GetLatestChatMessageStats 获取最新的100条消息的统计情况
func GetLatestChatMessageStats() (*ChatMessageStats, error) {
	var stats ChatMessageStats
	db := cosy.UseDB()

	// 设置当前日期
	stats.Date = time.Now().Format(time.DateOnly)

	// 直接从视图中查询数据
	var result struct {
		TotalPromptTokens int64   `json:"total_prompt_tokens"`
		TotalCompTokens   int64   `json:"total_comp_tokens"`
		TotalTokens       int64   `json:"total_tokens"`
		TotalPromptMS     int64   `json:"total_prompt_ms"`
		TotalCompMS       int64   `json:"total_comp_ms"`
		TotalMS           int64   `json:"total_ms"`
		AvgPromptMS       float64 `json:"avg_prompt_ms"`
		AvgCompMS         float64 `json:"avg_comp_ms"`
		AvgMS             float64 `json:"avg_ms"`
		ToolCallCount     int64   `json:"tool_call_count"`
	}

	err := db.Table(view.GetLatestChatMessageStatsViewName()).Find(&result).Error
	if err != nil {
		return nil, err
	}

	// 填充统计数据
	stats.TotalPromptTokens = result.TotalPromptTokens
	stats.TotalCompTokens = result.TotalCompTokens
	stats.TotalTokens = result.TotalTokens
	stats.TotalPromptMS = result.TotalPromptMS
	stats.TotalCompMS = result.TotalCompMS
	stats.TotalMS = result.TotalMS
	stats.AvgPromptMS = result.AvgPromptMS
	stats.AvgCompMS = result.AvgCompMS
	stats.AvgMS = result.AvgMS
	stats.ToolCallCount = result.ToolCallCount
	return &stats, nil
}
