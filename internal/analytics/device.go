package analytics

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// DeviceTypeDistribution 设备类型分布统计
type DeviceTypeDistribution struct {
	Name  string `json:"name"`  // 设备类型名称
	Count int64  `json:"count"` // 设备数量
}

// GetDeviceStatistics 获取设备相关统计数据
func GetDeviceStatistics() (*DeviceStatistics, error) {
	db := cosy.UseDB()
	stats := &DeviceStatistics{}

	// 查询设备总量
	if err := db.Model(&model.Device{}).Where("deleted_at = 0").Count(&stats.TotalDevices).Error; err != nil {
		return nil, err
	}

	// 查询已绑定设备总量（user_id 不为 0 的设备）
	if err := db.Model(&model.Device{}).Where("deleted_at = 0 AND user_id > 0").Count(&stats.BoundDevices).Error; err != nil {
		return nil, err
	}

	// 查询未绑定设备总量
	if err := db.Model(&model.Device{}).Where("deleted_at = 0 AND (user_id IS NULL OR user_id = 0)").Count(&stats.UnboundDevices).Error; err != nil {
		return nil, err
	}

	// 查询设备类型数量
	if err := db.Model(&model.DeviceType{}).Where("deleted_at = 0").Count(&stats.DeviceTypeCount).Error; err != nil {
		return nil, err
	}

	return stats, nil
}

// GetDeviceTypeDistribution 获取设备类型分布统计
func GetDeviceTypeDistribution() ([]DeviceTypeDistribution, error) {
	db := cosy.UseDB()
	var distributions []DeviceTypeDistribution

	stmt := gorm.Statement{DB: db}
	_ = stmt.Parse(&model.Device{})
	deviceTableName := stmt.Table

	stmt = gorm.Statement{DB: db}
	_ = stmt.Parse(&model.DeviceType{})
	deviceTypeTableName := stmt.Table

	// 查询每个设备类型的设备数量
	err := db.
		Table("`" + deviceTableName + "` devices").
		Select("device_types.name, COUNT(devices.id) as count").
		Joins("LEFT JOIN " + "`" + deviceTypeTableName + "` as device_types" + " ON devices.identifier = device_types.identifier").
		Where("devices.deleted_at = 0").
		Group("device_types.identifier, device_types.name").
		Order("count DESC").
		Find(&distributions).Error

	if err != nil {
		return nil, err
	}

	return distributions, nil
}
