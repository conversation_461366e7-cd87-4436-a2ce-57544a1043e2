package analytics

// SystemStatistics 系统相关统计数据
type SystemStatistics struct {
	RoleTemplateCount      int64                    `json:"role_template_count"`      // 角色模板总量
	OTACount               int64                    `json:"ota_count"`                // OTA 数量
	UserCount              int64                    `json:"user_count"`               // 用户数量
	ChatingDeviceCount     int64                    `json:"chating_device_count"`     // 正在对话的设备数量
	DailyRegister          []DailyRegisterStat      `json:"daily_register"`           // 近14天每日注册人数
	DeviceStats            DeviceStatistics         `json:"device_stats"`             // 设备相关统计数据
	DeviceTypeDistribution []DeviceTypeDistribution `json:"device_type_distribution"` // 设备类型分布统计
	ChatMessageStats       []ChatMessageStats       `json:"chat_message_stats"`       // 近14天聊天消息统计数据
	OTAReleases            []OTAReleaseStat         `json:"ota_releases"`             // 近14天OTA发版统计
	RoleTemplateUsage      []TemplateUsageStat      `json:"role_template_usage"`      // 近14天角色模板使用统计
}

// DeviceStatistics 设备相关统计数据
type DeviceStatistics struct {
	TotalDevices       int64 `json:"total_devices"`        // 设备总量
	BoundDevices       int64 `json:"bound_devices"`        // 已绑定设备量
	UnboundDevices     int64 `json:"unbound_devices"`      // 未绑定设备量
	DeviceTypeCount    int64 `json:"device_type_count"`    // 设备类型数量
	ChatingDeviceCount int64 `json:"chating_device_count"` // 正在对话的设备数量
}

// DailyRegisterStat 每日注册统计
type DailyRegisterStat struct {
	Date  string `json:"date"`  // 日期，格式为 YYYY-MM-DD
	Count int64  `json:"count"` // 当日注册人数
}

// OTAReleaseStat OTA发版统计
type OTAReleaseStat struct {
	Date  string `json:"date"`  // 日期，格式为 YYYY-MM-DD
	Count int64  `json:"count"` // 当日发版数量
}

// TemplateUsageStat 角色模板使用统计
type TemplateUsageStat struct {
	Date  string `json:"date"`  // 日期，格式为 YYYY-MM-DD
	Count int64  `json:"count"` // 当日使用次数
}

// ChatMessageStats 聊天消息统计
type ChatMessageStats struct {
	Date              string  `json:"date"`                // 日期，格式为 YYYY-MM-DD
	TotalPromptTokens int64   `json:"total_prompt_tokens"` // 当日提示词令牌总数
	TotalCompTokens   int64   `json:"total_comp_tokens"`   // 当日完成词令牌总数
	TotalTokens       int64   `json:"total_tokens"`        // 当日令牌总数
	TotalPromptMS     int64   `json:"total_prompt_ms"`     // 当日提示词处理时间总数(毫秒)
	TotalCompMS       int64   `json:"total_comp_ms"`       // 当日完成词处理时间总数(毫秒)
	TotalMS           int64   `json:"total_ms"`            // 当日处理时间总数(毫秒)
	AvgPromptMS       float64 `json:"avg_prompt_ms"`       // 当日平均提示词处理时间(毫秒)
	AvgCompMS         float64 `json:"avg_comp_ms"`         // 当日平均完成词处理时间(毫秒)
	AvgMS             float64 `json:"avg_ms"`              // 当日平均处理时间(毫秒)
	MessageCount      int64   `json:"message_count"`       // 当日消息数
	ToolCallCount     int64   `json:"tool_call_count"`     // 当日工具调用次数
}

// GetAllStatistics 获取所有统计数据
func GetAllStatistics() (*SystemStatistics, error) {
	// 获取设备相关统计
	deviceStats, err := GetDeviceStatistics()
	if err != nil {
		return nil, err
	}

	// 获取系统相关统计
	systemStats, err := GetSystemStatistics()
	if err != nil {
		return nil, err
	}

	// 获取设备类型分布统计
	deviceTypeDistribution, err := GetDeviceTypeDistribution()
	if err != nil {
		return nil, err
	}

	// 添加聊天设备统计数据到DeviceStatistics
	deviceStats.ChatingDeviceCount = int64(GetChatingDeviceCount())

	// 将设备统计数据添加到系统统计中
	systemStats.DeviceStats = *deviceStats
	systemStats.DeviceTypeDistribution = deviceTypeDistribution

	// 添加正在对话的设备数量到顶层
	systemStats.ChatingDeviceCount = deviceStats.ChatingDeviceCount

	return &systemStats, nil
}
