package analytics

import (
	"time"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

// GetOTACount 获取 OTA 数量
func GetOTACount() (int64, error) {
	db := cosy.UseDB()
	var count int64
	err := db.Model(&model.OTA{}).Where("deleted_at = 0").Count(&count).Error
	return count, err
}

// OTARelease 表示OTA发版统计数据
type OTARelease struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// GetOTAReleases 获取指定时间范围内的OTA发版统计
func GetOTAReleases(startDate, endDate time.Time) ([]OTARelease, error) {
	db := cosy.UseDB()
	var releases []OTARelease

	// 格式化日期为 YYYY-MM-DD 格式
	startDateStr := startDate.Format(time.DateOnly)
	endDateStr := endDate.Format(time.DateOnly)

	// 获取表名
	stmt := gorm.Statement{DB: db}
	_ = stmt.Parse(&model.OTA{})
	tableName := stmt.Table

	// 查询每天的OTA发版数量
	query := `
		SELECT
			DATE_FORMAT(FROM_UNIXTIME(created_at), '%Y-%m-%d') as date,
			COUNT(*) as count
		FROM ` + "`" + tableName + "`" + `
		WHERE
			created_at BETWEEN UNIX_TIMESTAMP(?) AND UNIX_TIMESTAMP(?)
			AND deleted_at = 0
		GROUP BY
			date
		ORDER BY
			date ASC
	`

	err := db.Raw(query, startDateStr, endDateStr).Scan(&releases).Error
	if err != nil {
		return nil, err
	}

	// 填充没有数据的日期
	result := fillMissingDates(startDate, endDate, releases, func(r OTARelease) string {
		return r.Date
	})

	return result, nil
}

// 填充缺失的日期，确保返回连续的日期数据
func fillMissingDates(startDate, endDate time.Time, data []OTARelease, getDate func(OTARelease) string) []OTARelease {
	// 创建日期映射
	dateMap := make(map[string]OTARelease)
	for _, item := range data {
		dateMap[getDate(item)] = item
	}

	// 填充缺失的日期
	var result []OTARelease
	currentDate := startDate

	for !currentDate.After(endDate) {
		dateStr := currentDate.Format(time.DateOnly)
		if item, exists := dateMap[dateStr]; exists {
			result = append(result, item)
		} else {
			result = append(result, OTARelease{
				Date:  dateStr,
				Count: 0,
			})
		}
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return result
}
