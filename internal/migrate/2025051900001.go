package migrate

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/go-gormigrate/gormigrate/v2"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type OldRoleTemplate struct {
	ID             uint64 `json:"id,string"`
	RoleCategoryID uint64 `json:"role_category_id,string"`
}

var TransfromRoleTemplateCategoryIDIntoIDs = &gormigrate.Migration{
	ID: "2025051900001",
	Migrate: func(tx *gorm.DB) error {
		var oldRoleTemplates []OldRoleTemplate
		tx.Model(&model.RoleTemplate{}).Find(&oldRoleTemplates)
		for _, oldRoleTemplate := range oldRoleTemplates {
			tx.Model(&model.RoleTemplate{}).
				Where("id = ?", oldRoleTemplate.ID).
				Updates(&model.RoleTemplate{
					RoleCategoryIDs: []string{cast.ToString(oldRoleTemplate.RoleCategoryID)},
				})
		}
		return nil
	},
}
