package migrate

import (
	"fmt"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

var ConvertRoleTemplatePromptCustomizableToPreset = &gormigrate.Migration{
	ID: "2025052000001",
	Migrate: func(tx *gorm.DB) error {
		stmt := gorm.Statement{DB: tx}
		_ = stmt.Parse(&model.RoleTemplate{})
		roleTemplateTableName := stmt.Table

		// 判断是否已存在 preset 字段
		if tx.Migrator().HasColumn(&model.RoleTemplate{}, "preset") {
			return nil
		}

		return tx.Exec(fmt.Sprintf("update `%s` set `preset` = NOT `prompt_customizable`",
			roleTemplateTableName,
		)).Error
	},
}
