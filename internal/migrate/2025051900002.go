package migrate

import (
	"fmt"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

var AddRoleTempleteIDToIntelligentAgent = &gormigrate.Migration{
	ID: "2025051900002",
	Migrate: func(tx *gorm.DB) error {
		stmt := gorm.Statement{DB: tx}
		_ = stmt.Parse(&model.IntelligentAgent{})
		intelligentAgentTableName := stmt.Table

		stmt = gorm.Statement{DB: tx}
		_ = stmt.Parse(&model.RoleTemplate{})
		roleTemplateTableName := stmt.Table

		return tx.Exec(fmt.Sprintf("update `%s` a join `%s` r on a.`name` = r.`name` set a.`role_template_id` = r.`id`",
			intelligentAgentTableName,
			roleTemplateTableName,
		)).Error
	},
}
