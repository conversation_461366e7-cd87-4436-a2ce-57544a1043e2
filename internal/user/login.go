package user

import (
	"errors"

	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/uozi-tech/cosy/logger"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrPasswordIncorrect = errors.New("password incorrect")
	ErrUserBanned        = errors.New("user banned")
)

func Login(email string, password string) (user *model.User, err error) {
	u := query.User

	user, err = u.Where(u.Email.Eq(email)).Preload(u.UserGroup).First()
	if err != nil {
		logger.Error(err)
		return nil, ErrPasswordIncorrect
	}

	if err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		logger.Error(err)
		return nil, ErrPasswordIncorrect
	}

	if user.Status == model.UserStatusBan {
		return nil, ErrUserBanned
	}

	return
}
