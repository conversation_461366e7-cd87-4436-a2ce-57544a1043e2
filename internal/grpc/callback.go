package grpc

import (
	"context"

	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (c *Client) CallBackUpdateMemory(sessionCtx *SessionContext) {
	ctx, cancel := context.WithTimeout(context.Background(), GrpcCallBackTimeout)
	defer cancel()
	qu := query.User

	userid := sessionCtx.Device.UserId
	logger.Debug("CallBackGetMemory.userid->", userid)
	var memory string

	qu.Where(qu.ID.Eq(userid)).Select(qu.Memory).Scan(&memory)
	logger.Debug("CallBackGetMemory历史对话请求:", c.getDialogueContext())
	voiceprint, err := GetSpeakerVoiceprint(sessionCtx.SpeakerId)
	if err != nil {
		if sessionCtx.SpeakerId != "-1" {
			// sessionCtx.SpeakerId = device.GetUserVoiceprint(sessionCtx.Agent.UserID)[0].Id
			logger.Error("查询声纹失败:", err)
			return
		}
		logger.Warn("查询声纹失败，但sessionCtx.SpeakerId为-1，跳过")
		voiceprint = &model.Voiceprint{
			Name:        "",
			Salutation:  "",
			Description: "",
		}
	}
	logger.Debug("CallBackGetMemory.voiceprint.name->", voiceprint.Name)
	resp, err := c.potatoChatClient.UpdateMemory(ctx, &proto.UpdateMemoryRequest{
		Context:            c.getDialogueContext(),
		MemoryContent:      memory, // 服务端会返回更新后的memory内容
		SpeakerName:        voiceprint.Name,
		SpeakerNickname:    voiceprint.Salutation,
		SpeakerDescription: voiceprint.Description,
	})
	if err != nil {
		if status.Code(err) == codes.Unimplemented {
			logger.Warn("UpdateMemory方法未实现，跳过")
			return
		}
		logger.Error(voiceprint.Salutation, "更新记忆失败CallBackGetMemory->UpdateMemory失败:", err)
		logger.Error(sessionCtx.Chat.User.Name, "更新记忆失败CallBackGetMemory->UpdateMemory失败:", err)
		return
	}

	res := resp.GetMemoryContent()
	logger.Debug("获取到的memory内容:", res)

	var user model.User

	qu.Where(qu.ID.Eq(userid)).Scan(&user)
	logger.Debug("CallBackGetMemory.userName->", user.Name)
	logger.Debug("CallBackGetMemory.userid>", user.ID)

	user.Memory = res
	qu.Where(qu.ID.Eq(userid)).Updates(user)
	logger.Debug("CallBackGetMemory完成，已更新用户memory")

}

// 一轮对话的标题
func (c *Client) CallBackConversationSummary(sessionCtx *SessionContext) {
	ctx, cancel := context.WithTimeout(context.Background(), GrpcCallBackTimeout)
	defer cancel()

	logger.Debug("开始获取对话摘要，历史对话:", c.getDialogueContext())

	resp, err := c.potatoChatClient.ConversationSummary(ctx, &proto.ConversationSummaryRequest{
		Context: c.getDialogueContext(),
	})
	if err != nil {
		if status.Code(err) == codes.Unimplemented {
			logger.Warn("ConversationSummary方法未实现，跳过")
			return
		}
		logger.Error(sessionCtx.Chat.User.Name, "更新标题失败,CallBackConversationSummary->ConversationSummary失败:", err)
		return
	}

	res := resp.GetSummary()
	logger.Debug("获取到的对话摘要:", res)

	qc := query.Chat
	if _, err := qc.Where(qc.ID.Eq(sessionCtx.Chat.ID)).Update(qc.Title, res); err != nil {
		logger.Error("更新对话标题失败:", err)
		return
	}
	logger.Debug("CallBackConversationSummary完成，已更新对话标题")
}

func (c *Client) CallBackSaveConversation4RAG(sessionCtx *SessionContext) {
	ctx, cancel := context.WithTimeout(context.Background(), GrpcCallBackTimeout)
	defer cancel()

	logger.Debug("开始获取RAG，历史对话:", c.getDialogueContext())

	qv := query.Voiceprint
	voiceprint, err := qv.Where(qv.ID.Eq(cast.ToUint64(sessionCtx.SpeakerId))).First()
	if err != nil {
		// sessionCtx.SpeakerId = device.GetUserVoiceprint(sessionCtx.Agent.UserID)[0].Id
		logger.Error("查询声纹失败:", err)
		voiceprint = &model.Voiceprint{
			Name:        "",
			Salutation:  "",
			Description: "",
		}
	}
	resp, err := c.potatoChatClient.SaveConversation4RAG(ctx, &proto.SaveConversation4RAGRequest{
		Context:         c.getDialogueContext(),
		SpeakerId:       sessionCtx.SpeakerId,
		SpeakerName:     voiceprint.Name,
		SpeakerNickname: voiceprint.Salutation,
		UserId:          cast.ToString(sessionCtx.Device.UserId),
	})
	if err != nil {
		if status.Code(err) == codes.Unimplemented {
			logger.Warn("SaveConversation4RAG方法未实现，跳过")
			return
		}
		logger.Error(voiceprint.Salutation, "rag失败,CallBackSaveConversation4RAG->SaveConversation4RAG失败:", err)
		logger.Error(sessionCtx.Chat.User.Name, "rag失败,CallBackSaveConversation4RAG->SaveConversation4RAG失败:", err)

		return
	}

	res := resp.GetSuccess()
	logger.Debug("获取到的RAG结果:", res)
	if !res {
		logger.Error("CallBackSaveConversation4RAG-grpc服务器更新失败")
	}
	logger.Debug("CallBackSaveConversation4RAG完成，已更新对话RAG")
}
