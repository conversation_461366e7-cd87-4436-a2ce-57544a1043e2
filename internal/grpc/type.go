package grpc

import (
	"git.uozi.org/uozi/potato-api/internal/device"
)

type TTSControlMsg struct {
	Type             string              `json:"type"`
	Transport        string              `json:"transport,omitempty"`
	AudioParams      *device.AudioParams `json:"audio_params,omitempty"`
	DeviceInfo       *device.DeviceInfo  `json:"device_info,omitempty"`
	SessionID        string              `json:"session_id,omitempty"`
	State            string              `json:"state,omitempty"`
	Mode             string              `json:"mode,omitempty"`
	Reason           string              `json:"reason,omitempty"`
	Text             string              `json:"text,omitempty"`
	Emotion          string              `json:"emotion,omitempty"`
	Commands         []*device.Command   `json:"commands,omitempty"`
	Time             int64               `json:"time,omitempty"`
	PromptTokens     uint                `json:"prompt_tokens,omitempty" `
	CompletionTokens uint                `json:"completion_tokens,omitempty" `
	TotalTokens      uint                `json:"total_tokens,omitempty" `
	PromptMS         uint                `json:"prompt_ms,omitempty"`
	CompletionMS     uint                `json:"completion_ms,omitempty"`
	TotalMS          float64             `json:"total_ms,omitempty"`
	Content          string              `json:"content,omitempty"`
	ToolCalls        string              `json:"tool_calls,omitempty"`
	ToolCallId       string              `json:"tool_call_id,omitempty"`
	Role             string              `json:"role,omitempty"`    //本次对话交互的角色
	RoleID           uint64              `json:"role_id,omitempty"` //用户更换智能体id
}
type ToolCall struct {
	ID       string
	Type     string
	Function Function
}
type Function struct {
	Name      string
	Arguments string
}
