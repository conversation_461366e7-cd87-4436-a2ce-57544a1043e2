package grpc

import (
	"context"
	"encoding/json"
	"net/url"
	"os"
	"time"

	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"git.uozi.org/uozi/potato-api/settings"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
)

const (
	GrpcAudioTime           = 300 * time.Second
	GrpcCallBackTimeout     = 60 * time.Second
	GrpcReConnectTimeout    = 1 * time.Second
	GrpcWaitAudioStreamInit = 20 * time.Millisecond
)

func (c *Client) UpAndDownAudio(sessionCtx *SessionContext) {
	qcm := query.ChatMessage
	chatmessageid := sessionCtx.UserChatMessageID
	var filePath string
	var err error

	// TODO
	uploadone := make(chan struct{})
	go func() {
		defer close(uploadone)
		filePath, err = device.OpusToWav(sessionCtx.UserOpusFrames, cast.ToString(chatmessageid))
		if err != nil {
			logger.Error("转换opus流失败:", err)
		}
		oss_url, _ := url.JoinPath(settings.OssSettings.BaseUrl, "potato/"+filePath)
		length, _ := device.GetAudioDuration(filePath)

		qcm.Where(qcm.ID.Eq(chatmessageid)).Updates(&model.ChatMessage{
			AsrURL: oss_url,
			Length: uint(length.Seconds()),
		})
		err := device.UploadAudio(filePath)
		if err != nil {
			logger.Error(err)
		}
	}()
	go func() {
		<-uploadone
		err = os.Remove(filePath)
		if err != nil {
			logger.Error(err)
		}
		logger.Debug("os.remove->", filePath)
		sessionCtx.UserOpusFrames = make([][]byte, 0)
	}()

}

func (c *Client) triggerDialogueInteraction(sessionCtx *SessionContext, vector []float32) {
	c.mu.Lock()
	tmptext := c.Text
	c.mu.Unlock()

	logger.Info("大模型的翻译->", tmptext)
	// 协程去opus上oss上传，数据库操作依然是堵塞执行
	c.InsertUserAsrDb_Context(sessionCtx, tmptext, vector)

}

func (c *Client) InsertDialogueTTSContext(sessionCtx *SessionContext, role model.ChatMessageRole, tts TTSControlMsg) {
	c.dialogContext = append(c.dialogContext, &proto.DialogueContext{
		Role:       string(role),
		Content:    tts.Content,
		ToolCallId: tts.ToolCallId,
		ToolCalls:  cast.ToString(tts.ToolCalls),
	})
}
func (c *Client) InsertAIDialogueTTSDb(sessionCtx *SessionContext, tts model.ChatMessage) {
	qcm := query.ChatMessage
	content := tts.Content
	aichatmessage := &model.ChatMessage{
		UserID:     sessionCtx.Device.UserId,
		ChatID:     sessionCtx.Chat.ID,
		Role:       tts.Role,
		Content:    content,
		ToolCalls:  tts.ToolCalls,
		ToolCallId: tts.ToolCallId,
	}
	qcm.Create(aichatmessage)
	// TODO ,应该只在tool调用的回复那次赋值，不会这样确实会覆盖
	sessionCtx.AIChatMessageID = aichatmessage.ID
	logger.Info("大模型回复的content->", content)
}
func (c *Client) InsertUserAsrDb_Context(sessionCtx *SessionContext, text string, vector []float32) {
	qcm := query.ChatMessage

	err := query.Chat.Save(sessionCtx.Chat)
	if err != nil {
		logger.Error(err)
		logger.Error("保存对话记录失败->", sessionCtx.Chat.ID)
	}
	// 存储时
	embeddingBytes, _ := json.Marshal(vector)
	str_vector := string(embeddingBytes)

	logger.Debug("str_vector:", str_vector)
	chatmessage := &model.ChatMessage{
		UserID:    sessionCtx.Device.UserId,
		ChatID:    sessionCtx.Chat.ID,
		Role:      model.ChatMessageRoleUser,
		Content:   text,
		Embedding: str_vector,
		States:    sessionCtx.DeviceState,
	}
	logger.Debug(" InsertUserAsrDb_Context.sessionCtx.States:", sessionCtx.DeviceState)

	qcm.Create(chatmessage)
	sessionCtx.UserChatMessageID = chatmessage.ID
	c.UpAndDownAudio(sessionCtx)

	c.dialogContext = append(c.dialogContext, &proto.DialogueContext{
		Role:    string(model.ChatMessageRoleUser),
		Content: text,
	})
}
func (c *Client) UpdateAIDialogueTTSDb(sessionCtx *SessionContext, tts model.ChatMessage) {
	qcm := query.ChatMessage
	logger.Debug("sessionCtx2.ChatMessageID->", sessionCtx.AIChatMessageID)
	qcm.Where(qcm.ID.Eq(sessionCtx.AIChatMessageID)).Updates(
		&model.ChatMessage{
			// ToolCallId: tts.ToolCallId,
			Content: tts.Content,
		})

}

func (c *Client) SetSessionID(sessionID string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.sessionID = sessionID
	logger.Debug("已设置gRPC客户端会话ID:", sessionID)
}
func (c *Client) GetSessionCtx() (*SessionContext, bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	res, ok := SessionMap.Load(c.sessionID)
	if !ok {
		return nil, false
	}
	sc, ok := res.(*SessionContext)
	return sc, ok
}
func (c *Client) getDialogueContext() []*proto.DialogueContext {
	// logger.Debug("短期上下文->", c.dialogContext)
	return c.dialogContext
}
func GetSpeakerVoiceprint(speakerID string) (*model.Voiceprint, error) {

	qv := query.Voiceprint
	voiceprint, err := qv.Where(qv.ID.Eq(cast.ToUint64(speakerID))).First()
	if err != nil {
		return nil, err
	}
	return voiceprint, nil
}

// 这里查询了数据库，导致前面不能用协程去跑
func (c *Client) GetDialogueRequest(sessionCtx *SessionContext) *proto.DialogueRequest {
	qu := query.User
	userid := sessionCtx.Device.UserId
	var memory string
	qu.Where(qu.ID.Eq(userid)).Select(qu.Memory).Scan(&memory)
	logger.Debug("GetDialogueRequest.sessionCtx.SpeakerId->", sessionCtx.SpeakerId)
	voiceprint, err := GetSpeakerVoiceprint(sessionCtx.SpeakerId)
	if err != nil {
		// sessionCtx.SpeakerId = device.GetUserVoiceprint(sessionCtx.Agent.UserID)[0].Id
		logger.Error("查询声纹失败:", err)
		logger.Error(voiceprint)
		voiceprint = &model.Voiceprint{
			Name:        "",
			Salutation:  "",
			Description: "",
		}
	}
	role := sessionCtx.Agent.GetRole()
	// 创建对话请求
	req := &proto.DialogueRequest{
		// Context:        sessionCtx.GrpcClient.getDialogueContext(),报错
		SpeakerId: sessionCtx.SpeakerId,
		Context:   c.getDialogueContext(),

		RoleName:       role.Name,      //智能体名称
		RoleDefinition: role.Prompt,    //智能体定义，提示词
		VoiceType:      role.VoiceType, // 智能体音色

		LlmType:            sessionCtx.Agent.LLM, // 智能体大模型
		Memory:             memory,
		SpeakerName:        voiceprint.Name,
		SpeakerNickname:    voiceprint.Salutation,
		SpeakerDescription: voiceprint.Description,
		UserId:             cast.ToString(userid),
		Language:           sessionCtx.Agent.Language,
		RoleList:           sessionCtx.AgentList,
		DeivceState: &proto.DeivceState{
			Volume:   int32(sessionCtx.DeviceState.Volume),
			Level:    int32(sessionCtx.DeviceState.Level),
			Charging: sessionCtx.DeviceState.Charging,
		},
	}
	logger.Info("GetDialogueRequest.req->", req)
	return req
}

func (c *Client) GetContext() context.Context {
	return c.ctx
}

func (c *Client) GetTTSMsg() TTSControlMsg {
	return c.ttsMsg
}
