package grpc

import (
	"encoding/json"
	"io"
	"time"

	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gorilla/websocket"
	"github.com/uozi-tech/cosy/logger"
)

var i = 1
var tmp int64

func init() {
	RegisterHandler("dialogue_interaction", DialogueInteractionHandler)
}
func DialogueInteractionHandler(c *Client) {
	// 处理响应流
	sessionctx, ok := c.GetSessionCtx()
	if !ok {
		logger.Error("sessionCtx not found")
		return
	}
	for {
		resp, err := c.dialogStream.Recv()
		if err == io.EOF {
			logger.Debug("接收交互返回的语音流结束")
			break
		}
		if err != nil {
			logger.Error("接收对话响应失败:", err)
			break
		}
		// 打断交互接口写入websocket的动作
		if c.IsReturnWsPause {
			logger.Debug("打断交互接口写入websocket的动作")
			c.IsReturnWsPause = false
			break
		}
		switch content := resp.Content.(type) {
		case *proto.DialogueResponse_ControlMessage:

			logger.Debug("[控制消息] %s", content.ControlMessage)
			controlMsg := content.ControlMessage
			var ttsMsg TTSControlMsg
			err := json.Unmarshal([]byte(controlMsg), &ttsMsg)
			if err != nil {
				logger.Error("解析TTS控制消息失败:", err)
				logger.Debug("预处理后的消息内容:", controlMsg)
				continue
			}
			switch ttsMsg.Type {
			case "tts":
				switch ttsMsg.State {
				case "start":
					handler, ok := device.GetHandler("tts_start")
					if ok {
						handler(sessionctx.WebSocketFrame)
					}
				case "sentence_start":
					handler, ok := device.GetHandler("tts_sentence")
					if ok {
						sessionctx.WebSocketFrame.Data.Text = ttsMsg.Text
						handler(sessionctx.WebSocketFrame)
					}
				case "stop":
					handler, ok := device.GetHandler("tts_stop")
					if ok {
						handler(sessionctx.WebSocketFrame)
					}
				}
			case "llm":
				handler, ok := device.GetHandler("llm_emotion")
				if ok {
					sessionctx.WebSocketFrame.Data.Text = ttsMsg.Text
					sessionctx.WebSocketFrame.Data.Emotion = ttsMsg.Emotion
					handler(sessionctx.WebSocketFrame)
				}
			case "llm_consumption":
				handler, ok := GetHandler("llm_token")
				if ok {
					c.ttsMsg = ttsMsg
					handler(c)
				}
			case "end_chat":
				handler, ok := device.GetHandler("end_chat")
				if ok {
					handler(sessionctx.WebSocketFrame)
				}
				logger.Debug("ttsMsg->", ttsMsg)
			case "llm_message":
				tmp_tts_msg := model.ChatMessage{
					Role:       model.ChatMessageRole(ttsMsg.Role),
					Content:    ttsMsg.Content,
					ToolCalls:  ttsMsg.ToolCalls,
					ToolCallId: ttsMsg.ToolCallId}

				c.InsertDialogueTTSContext(sessionctx, model.ChatMessageRole(ttsMsg.Role), ttsMsg)
				c.InsertAIDialogueTTSDb(sessionctx, tmp_tts_msg)
			case "iot":
				handler, ok := device.GetHandler("iot")
				if ok {
					sessionctx.WebSocketFrame.Data.Type = ttsMsg.Type
					sessionctx.WebSocketFrame.Data.SessionID = ttsMsg.SessionID
					sessionctx.WebSocketFrame.Data.Commands = ttsMsg.Commands
					handler(sessionctx.WebSocketFrame)
				}
			case "switch_role":
				// 更改智能体id
				qd := query.Device
				logger.Info("切换智能体ttsmsg", ttsMsg)
				qd.Where(qd.ID.Eq(sessionctx.Device.ID)).Update(qd.IntelligentAgentID, ttsMsg.RoleID)
			}

		case *proto.DialogueResponse_AudioStream:
			if i == 1 {
				Time2 := time.Now().UnixNano()
				logger.Debug("从收到isfinnal,asr返回结果，go开始请求，到返回的第一条语音消耗的时间", Time2-Time1)
				i++
			}
			// 发送音频数据到WebSocket
			// fmt.Print(".")

			// 然后在每次处理 AudioStream 消息时计算间隔
			tmp1 := time.Now().UnixMilli()
			res := tmp1 - tmp // 将纳秒转换为毫秒
			logger.Debug("毫秒差:", res)
			tmp = tmp1
			if res > 100 {
			    logger.Warn("音频流处理时间过长:",res)
			}
			err := sessionctx.WebSocketFrame.WriteMessage(
				websocket.BinaryMessage,
				content.AudioStream,
			)
			if err != nil {
				logger.Error("发送音频流到WebSocket失败:", err)
				return
			}
		case *proto.DialogueResponse_AudioParams:
			handler, ok := device.GetHandler("audio_params")
			if ok {
				sessionctx.WebSocketFrame.Data.AudioParams.SampleRate = int(content.AudioParams.SampleRate)
				sessionctx.WebSocketFrame.Data.AudioParams.Channels = int(content.AudioParams.Channels)
				sessionctx.WebSocketFrame.Data.AudioParams.FrameDuration = int(content.AudioParams.FrameDuration)
				sessionctx.WebSocketFrame.Data.AudioParams.Format = content.AudioParams.Format
				handler(sessionctx.WebSocketFrame)
			}

		}
	}
	i = 1
}

/*

jsonData := `{
	"type": "iot",
	"commands": [
		{
			"name": "Speaker",
			"method": "SetVolume",
			"parameters": {
				"volume": 55
			}
		}
	],
	"session_id": "55c9d225"
}`
var ttsMsg1 TTSControlMsg
err := json.Unmarshal([]byte(jsonData), &ttsMsg1)
if err != nil {
	logger.Error("解析IOT控制消息失败:", err)
	continue
}
logger.Debug("iot->", ttsMsg.Commands)
handler1, ok := device.GetHandler("iot")
if ok {
	sessionctx.WebSocketFrame.Data.Type = ttsMsg1.Type
	sessionctx.WebSocketFrame.Data.SessionID = ttsMsg1.SessionID
	sessionctx.WebSocketFrame.Data.Commands = ttsMsg1.Commands
	logger.Debug("ttsMsg.Type->", ttsMsg1.Type)
	logger.Debug("ttsMsg.SessionID->", ttsMsg1.SessionID)
	logger.Debug("ttsMsg.Commands->", ttsMsg1.Commands)
	handler1(sessionctx.WebSocketFrame)
}
*/
