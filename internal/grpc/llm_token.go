package grpc

import (
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/uozi-tech/cosy/logger"
)

func init() {
	RegisterHandler("llm_token", LLMToken)
}
func LLMToken(c *Client) {
	logger.Debug("LLMToken->c.TTSMsg:", c.GetTTSMsg())
	sessionctx, ok := c.GetSessionCtx()
	ttsmsg := c.GetTTSMsg()
	if !ok {
		logger.Error("LLMToken sessionCtx not found")
		return
	}
	qcm := query.ChatMessage
	// TODO 需要根据grpc返回的顺序来决定是save还是update
	qcm.Where(qcm.ID.Eq(sessionctx.AIChatMessageID)).Updates(&model.ChatMessage{
		PromptTokens:     ttsmsg.PromptTokens,
		CompletionMS:     ttsmsg.CompletionMS,
		TotalTokens:      ttsmsg.TotalTokens,
		CompletionTokens: ttsmsg.CompletionTokens,
		PromptMS:         ttsmsg.PromptMS,
		TotalMS:          uint(ttsmsg.TotalMS),
	})
}
