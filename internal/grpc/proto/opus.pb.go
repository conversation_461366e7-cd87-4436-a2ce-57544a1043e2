// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.21.12
// source: opus.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Ping API messages
type PingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SessionId     string                 `protobuf:"bytes,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	mi := &file_opus_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{0}
}

func (x *PingRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type PingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingResponse) Reset() {
	*x = PingResponse{}
	mi := &file_opus_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResponse) ProtoMessage() {}

func (x *PingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResponse.ProtoReflect.Descriptor instead.
func (*PingResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{1}
}

func (x *PingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Audio Stream Recognition messages
type AudioStreamRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Request:
	//
	//	*AudioStreamRequest_InitialConfig
	//	*AudioStreamRequest_AudioData
	//	*AudioStreamRequest_ClientControlMessage
	Request       isAudioStreamRequest_Request `protobuf_oneof:"request"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioStreamRequest) Reset() {
	*x = AudioStreamRequest{}
	mi := &file_opus_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioStreamRequest) ProtoMessage() {}

func (x *AudioStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioStreamRequest.ProtoReflect.Descriptor instead.
func (*AudioStreamRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{2}
}

func (x *AudioStreamRequest) GetRequest() isAudioStreamRequest_Request {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *AudioStreamRequest) GetInitialConfig() *InitialConfig {
	if x != nil {
		if x, ok := x.Request.(*AudioStreamRequest_InitialConfig); ok {
			return x.InitialConfig
		}
	}
	return nil
}

func (x *AudioStreamRequest) GetAudioData() []byte {
	if x != nil {
		if x, ok := x.Request.(*AudioStreamRequest_AudioData); ok {
			return x.AudioData
		}
	}
	return nil
}

func (x *AudioStreamRequest) GetClientControlMessage() string {
	if x != nil {
		if x, ok := x.Request.(*AudioStreamRequest_ClientControlMessage); ok {
			return x.ClientControlMessage
		}
	}
	return ""
}

type isAudioStreamRequest_Request interface {
	isAudioStreamRequest_Request()
}

type AudioStreamRequest_InitialConfig struct {
	InitialConfig *InitialConfig `protobuf:"bytes,1,opt,name=initial_config,json=initialConfig,proto3,oneof"`
}

type AudioStreamRequest_AudioData struct {
	AudioData []byte `protobuf:"bytes,2,opt,name=audio_data,json=audioData,proto3,oneof"` // OPUS audio stream
}

type AudioStreamRequest_ClientControlMessage struct {
	ClientControlMessage string `protobuf:"bytes,3,opt,name=client_control_message,json=clientControlMessage,proto3,oneof"`
}

func (*AudioStreamRequest_InitialConfig) isAudioStreamRequest_Request() {}

func (*AudioStreamRequest_AudioData) isAudioStreamRequest_Request() {}

func (*AudioStreamRequest_ClientControlMessage) isAudioStreamRequest_Request() {}

type InitialConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Voiceprints   []*Voiceprint          `protobuf:"bytes,1,rep,name=voiceprints,proto3" json:"voiceprints,omitempty"`
	AudioParams   *AudioParams           `protobuf:"bytes,2,opt,name=audio_params,json=audioParams,proto3" json:"audio_params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitialConfig) Reset() {
	*x = InitialConfig{}
	mi := &file_opus_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitialConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitialConfig) ProtoMessage() {}

func (x *InitialConfig) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitialConfig.ProtoReflect.Descriptor instead.
func (*InitialConfig) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{3}
}

func (x *InitialConfig) GetVoiceprints() []*Voiceprint {
	if x != nil {
		return x.Voiceprints
	}
	return nil
}

func (x *InitialConfig) GetAudioParams() *AudioParams {
	if x != nil {
		return x.AudioParams
	}
	return nil
}

type Voiceprint struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SpeakerName     string                 `protobuf:"bytes,2,opt,name=speaker_name,json=speakerName,proto3" json:"speaker_name,omitempty"`             // 说话人名称
	SpeakerNickname string                 `protobuf:"bytes,3,opt,name=speaker_nickname,json=speakerNickname,proto3" json:"speaker_nickname,omitempty"` // 说话人称呼
	Vector          []float32              `protobuf:"fixed32,4,rep,packed,name=vector,proto3" json:"vector,omitempty"`                                 // 特征向量是一个256维的浮点数组
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Voiceprint) Reset() {
	*x = Voiceprint{}
	mi := &file_opus_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Voiceprint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voiceprint) ProtoMessage() {}

func (x *Voiceprint) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voiceprint.ProtoReflect.Descriptor instead.
func (*Voiceprint) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{4}
}

func (x *Voiceprint) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Voiceprint) GetSpeakerName() string {
	if x != nil {
		return x.SpeakerName
	}
	return ""
}

func (x *Voiceprint) GetSpeakerNickname() string {
	if x != nil {
		return x.SpeakerNickname
	}
	return ""
}

func (x *Voiceprint) GetVector() []float32 {
	if x != nil {
		return x.Vector
	}
	return nil
}

type AudioParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Format        string                 `protobuf:"bytes,1,opt,name=format,proto3" json:"format,omitempty"`                                     // e.g., "opus"
	SampleRate    int32                  `protobuf:"varint,2,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`          // e.g., 16000
	Channels      int32                  `protobuf:"varint,3,opt,name=channels,proto3" json:"channels,omitempty"`                                // e.g., 1
	FrameDuration int32                  `protobuf:"varint,4,opt,name=frame_duration,json=frameDuration,proto3" json:"frame_duration,omitempty"` // e.g., 60
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioParams) Reset() {
	*x = AudioParams{}
	mi := &file_opus_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioParams) ProtoMessage() {}

func (x *AudioParams) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioParams.ProtoReflect.Descriptor instead.
func (*AudioParams) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{5}
}

func (x *AudioParams) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *AudioParams) GetSampleRate() int32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *AudioParams) GetChannels() int32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

func (x *AudioParams) GetFrameDuration() int32 {
	if x != nil {
		return x.FrameDuration
	}
	return 0
}

// ASR Result structure
type AsrResult struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	RecognizedText   string                 `protobuf:"bytes,1,opt,name=recognized_text,json=recognizedText,proto3" json:"recognized_text,omitempty"`
	VoiceprintVector []float32              `protobuf:"fixed32,2,rep,packed,name=voiceprint_vector,json=voiceprintVector,proto3" json:"voiceprint_vector,omitempty"`
	SpeakerId        string                 `protobuf:"bytes,3,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id,omitempty"`
	IsFinal          bool                   `protobuf:"varint,4,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AsrResult) Reset() {
	*x = AsrResult{}
	mi := &file_opus_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsrResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsrResult) ProtoMessage() {}

func (x *AsrResult) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsrResult.ProtoReflect.Descriptor instead.
func (*AsrResult) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{6}
}

func (x *AsrResult) GetRecognizedText() string {
	if x != nil {
		return x.RecognizedText
	}
	return ""
}

func (x *AsrResult) GetVoiceprintVector() []float32 {
	if x != nil {
		return x.VoiceprintVector
	}
	return nil
}

func (x *AsrResult) GetSpeakerId() string {
	if x != nil {
		return x.SpeakerId
	}
	return ""
}

func (x *AsrResult) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

type AudioRecognitionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Response:
	//
	//	*AudioRecognitionResponse_AsrResult
	//	*AudioRecognitionResponse_ServerControlMessage
	Response      isAudioRecognitionResponse_Response `protobuf_oneof:"response"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AudioRecognitionResponse) Reset() {
	*x = AudioRecognitionResponse{}
	mi := &file_opus_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AudioRecognitionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudioRecognitionResponse) ProtoMessage() {}

func (x *AudioRecognitionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudioRecognitionResponse.ProtoReflect.Descriptor instead.
func (*AudioRecognitionResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{7}
}

func (x *AudioRecognitionResponse) GetResponse() isAudioRecognitionResponse_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *AudioRecognitionResponse) GetAsrResult() *AsrResult {
	if x != nil {
		if x, ok := x.Response.(*AudioRecognitionResponse_AsrResult); ok {
			return x.AsrResult
		}
	}
	return nil
}

func (x *AudioRecognitionResponse) GetServerControlMessage() string {
	if x != nil {
		if x, ok := x.Response.(*AudioRecognitionResponse_ServerControlMessage); ok {
			return x.ServerControlMessage
		}
	}
	return ""
}

type isAudioRecognitionResponse_Response interface {
	isAudioRecognitionResponse_Response()
}

type AudioRecognitionResponse_AsrResult struct {
	AsrResult *AsrResult `protobuf:"bytes,1,opt,name=asr_result,json=asrResult,proto3,oneof"`
}

type AudioRecognitionResponse_ServerControlMessage struct {
	ServerControlMessage string `protobuf:"bytes,2,opt,name=server_control_message,json=serverControlMessage,proto3,oneof"`
}

func (*AudioRecognitionResponse_AsrResult) isAudioRecognitionResponse_Response() {}

func (*AudioRecognitionResponse_ServerControlMessage) isAudioRecognitionResponse_Response() {}

// Dialogue Interaction messages
type DialogueRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Context            []*DialogueContext     `protobuf:"bytes,1,rep,name=context,proto3" json:"context,omitempty"`
	RoleName           string                 `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`                               // 角色名称
	RoleDefinition     string                 `protobuf:"bytes,3,opt,name=role_definition,json=roleDefinition,proto3" json:"role_definition,omitempty"`             // 角色定义
	Memory             string                 `protobuf:"bytes,4,opt,name=memory,proto3" json:"memory,omitempty"`                                                   // 短期记忆
	VoiceType          string                 `protobuf:"bytes,5,opt,name=voice_type,json=voiceType,proto3" json:"voice_type,omitempty"`                            // 角色音色
	LlmType            string                 `protobuf:"bytes,6,opt,name=llm_type,json=llmType,proto3" json:"llm_type,omitempty"`                                  // 角色模型
	SpeakerName        string                 `protobuf:"bytes,7,opt,name=speaker_name,json=speakerName,proto3" json:"speaker_name,omitempty"`                      // 说话人名称
	SpeakerNickname    string                 `protobuf:"bytes,8,opt,name=speaker_nickname,json=speakerNickname,proto3" json:"speaker_nickname,omitempty"`          // 说话人称呼
	SpeakerDescription string                 `protobuf:"bytes,9,opt,name=speaker_description,json=speakerDescription,proto3" json:"speaker_description,omitempty"` // 说话人描述
	UserId             string                 `protobuf:"bytes,10,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                    // 用户id，用于rag知识库存储和查询
	SpeakerId          string                 `protobuf:"bytes,11,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id,omitempty"`                           // 说话人id
	DeivceState        *DeivceState           `protobuf:"bytes,12,opt,name=deivce_state,json=deivceState,proto3" json:"deivce_state,omitempty"`                     // 设备状态
	RoleList           []*RoleList            `protobuf:"bytes,13,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`                              // 用户角色列表
	Language           string                 `protobuf:"bytes,14,opt,name=language,proto3" json:"language,omitempty"`                                              // 指定语言偏好，中文：zh；英文：en；未指定为空。
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DialogueRequest) Reset() {
	*x = DialogueRequest{}
	mi := &file_opus_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DialogueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogueRequest) ProtoMessage() {}

func (x *DialogueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogueRequest.ProtoReflect.Descriptor instead.
func (*DialogueRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{8}
}

func (x *DialogueRequest) GetContext() []*DialogueContext {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *DialogueRequest) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

func (x *DialogueRequest) GetRoleDefinition() string {
	if x != nil {
		return x.RoleDefinition
	}
	return ""
}

func (x *DialogueRequest) GetMemory() string {
	if x != nil {
		return x.Memory
	}
	return ""
}

func (x *DialogueRequest) GetVoiceType() string {
	if x != nil {
		return x.VoiceType
	}
	return ""
}

func (x *DialogueRequest) GetLlmType() string {
	if x != nil {
		return x.LlmType
	}
	return ""
}

func (x *DialogueRequest) GetSpeakerName() string {
	if x != nil {
		return x.SpeakerName
	}
	return ""
}

func (x *DialogueRequest) GetSpeakerNickname() string {
	if x != nil {
		return x.SpeakerNickname
	}
	return ""
}

func (x *DialogueRequest) GetSpeakerDescription() string {
	if x != nil {
		return x.SpeakerDescription
	}
	return ""
}

func (x *DialogueRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DialogueRequest) GetSpeakerId() string {
	if x != nil {
		return x.SpeakerId
	}
	return ""
}

func (x *DialogueRequest) GetDeivceState() *DeivceState {
	if x != nil {
		return x.DeivceState
	}
	return nil
}

func (x *DialogueRequest) GetRoleList() []*RoleList {
	if x != nil {
		return x.RoleList
	}
	return nil
}

func (x *DialogueRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type DialogueContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          string                 `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"` // "system", "assistant", "tool", or "user"
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	ToolCalls     string                 `protobuf:"bytes,3,opt,name=tool_calls,json=toolCalls,proto3" json:"tool_calls,omitempty"`      // assistant专属，大模型给出的调用方法名称和相关参数
	ToolCallId    string                 `protobuf:"bytes,4,opt,name=tool_call_id,json=toolCallId,proto3" json:"tool_call_id,omitempty"` // tool专属，工具调用的id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DialogueContext) Reset() {
	*x = DialogueContext{}
	mi := &file_opus_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DialogueContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogueContext) ProtoMessage() {}

func (x *DialogueContext) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogueContext.ProtoReflect.Descriptor instead.
func (*DialogueContext) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{9}
}

func (x *DialogueContext) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *DialogueContext) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DialogueContext) GetToolCalls() string {
	if x != nil {
		return x.ToolCalls
	}
	return ""
}

func (x *DialogueContext) GetToolCallId() string {
	if x != nil {
		return x.ToolCallId
	}
	return ""
}

type DeivceState struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Volume        int32                  `protobuf:"varint,1,opt,name=volume,proto3" json:"volume,omitempty"`     // 当前设备音量
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`       // 当前设备电量
	Charging      bool                   `protobuf:"varint,3,opt,name=charging,proto3" json:"charging,omitempty"` // 当前设备是否在充电
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeivceState) Reset() {
	*x = DeivceState{}
	mi := &file_opus_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeivceState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeivceState) ProtoMessage() {}

func (x *DeivceState) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeivceState.ProtoReflect.Descriptor instead.
func (*DeivceState) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{10}
}

func (x *DeivceState) GetVolume() int32 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *DeivceState) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *DeivceState) GetCharging() bool {
	if x != nil {
		return x.Charging
	}
	return false
}

type RoleList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 角色id
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 角色名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleList) Reset() {
	*x = RoleList{}
	mi := &file_opus_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleList) ProtoMessage() {}

func (x *RoleList) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleList.ProtoReflect.Descriptor instead.
func (*RoleList) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{11}
}

func (x *RoleList) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RoleList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DialogueResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*DialogueResponse_ControlMessage
	//	*DialogueResponse_AudioStream
	//	*DialogueResponse_AudioParams
	Content       isDialogueResponse_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DialogueResponse) Reset() {
	*x = DialogueResponse{}
	mi := &file_opus_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DialogueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DialogueResponse) ProtoMessage() {}

func (x *DialogueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DialogueResponse.ProtoReflect.Descriptor instead.
func (*DialogueResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{12}
}

func (x *DialogueResponse) GetContent() isDialogueResponse_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DialogueResponse) GetControlMessage() string {
	if x != nil {
		if x, ok := x.Content.(*DialogueResponse_ControlMessage); ok {
			return x.ControlMessage
		}
	}
	return ""
}

func (x *DialogueResponse) GetAudioStream() []byte {
	if x != nil {
		if x, ok := x.Content.(*DialogueResponse_AudioStream); ok {
			return x.AudioStream
		}
	}
	return nil
}

func (x *DialogueResponse) GetAudioParams() *AudioParams {
	if x != nil {
		if x, ok := x.Content.(*DialogueResponse_AudioParams); ok {
			return x.AudioParams
		}
	}
	return nil
}

type isDialogueResponse_Content interface {
	isDialogueResponse_Content()
}

type DialogueResponse_ControlMessage struct {
	ControlMessage string `protobuf:"bytes,1,opt,name=control_message,json=controlMessage,proto3,oneof"` // TTS start, TTS text, TTS end, IoT control, etc.
}

type DialogueResponse_AudioStream struct {
	AudioStream []byte `protobuf:"bytes,2,opt,name=audio_stream,json=audioStream,proto3,oneof"` // OPUS audio stream
}

type DialogueResponse_AudioParams struct {
	AudioParams *AudioParams `protobuf:"bytes,3,opt,name=audio_params,json=audioParams,proto3,oneof"`
}

func (*DialogueResponse_ControlMessage) isDialogueResponse_Content() {}

func (*DialogueResponse_AudioStream) isDialogueResponse_Content() {}

func (*DialogueResponse_AudioParams) isDialogueResponse_Content() {}

// Update Memory messages
type UpdateMemoryRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Context            []*DialogueContext     `protobuf:"bytes,1,rep,name=context,proto3" json:"context,omitempty"`
	MemoryContent      string                 `protobuf:"bytes,2,opt,name=memory_content,json=memoryContent,proto3" json:"memory_content,omitempty"`
	SpeakerName        string                 `protobuf:"bytes,3,opt,name=speaker_name,json=speakerName,proto3" json:"speaker_name,omitempty"`                      // 说话人名称
	SpeakerNickname    string                 `protobuf:"bytes,4,opt,name=speaker_nickname,json=speakerNickname,proto3" json:"speaker_nickname,omitempty"`          // 说话人称呼
	SpeakerDescription string                 `protobuf:"bytes,5,opt,name=speaker_description,json=speakerDescription,proto3" json:"speaker_description,omitempty"` // 说话人描述
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UpdateMemoryRequest) Reset() {
	*x = UpdateMemoryRequest{}
	mi := &file_opus_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemoryRequest) ProtoMessage() {}

func (x *UpdateMemoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemoryRequest.ProtoReflect.Descriptor instead.
func (*UpdateMemoryRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateMemoryRequest) GetContext() []*DialogueContext {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *UpdateMemoryRequest) GetMemoryContent() string {
	if x != nil {
		return x.MemoryContent
	}
	return ""
}

func (x *UpdateMemoryRequest) GetSpeakerName() string {
	if x != nil {
		return x.SpeakerName
	}
	return ""
}

func (x *UpdateMemoryRequest) GetSpeakerNickname() string {
	if x != nil {
		return x.SpeakerNickname
	}
	return ""
}

func (x *UpdateMemoryRequest) GetSpeakerDescription() string {
	if x != nil {
		return x.SpeakerDescription
	}
	return ""
}

type UpdateMemoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MemoryContent string                 `protobuf:"bytes,1,opt,name=memory_content,json=memoryContent,proto3" json:"memory_content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMemoryResponse) Reset() {
	*x = UpdateMemoryResponse{}
	mi := &file_opus_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMemoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMemoryResponse) ProtoMessage() {}

func (x *UpdateMemoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMemoryResponse.ProtoReflect.Descriptor instead.
func (*UpdateMemoryResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateMemoryResponse) GetMemoryContent() string {
	if x != nil {
		return x.MemoryContent
	}
	return ""
}

// Conversation Summary messages
type ConversationSummaryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Context       []*DialogueContext     `protobuf:"bytes,1,rep,name=context,proto3" json:"context,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConversationSummaryRequest) Reset() {
	*x = ConversationSummaryRequest{}
	mi := &file_opus_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConversationSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationSummaryRequest) ProtoMessage() {}

func (x *ConversationSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationSummaryRequest.ProtoReflect.Descriptor instead.
func (*ConversationSummaryRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{15}
}

func (x *ConversationSummaryRequest) GetContext() []*DialogueContext {
	if x != nil {
		return x.Context
	}
	return nil
}

type ConversationSummaryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Summary       string                 `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConversationSummaryResponse) Reset() {
	*x = ConversationSummaryResponse{}
	mi := &file_opus_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConversationSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationSummaryResponse) ProtoMessage() {}

func (x *ConversationSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationSummaryResponse.ProtoReflect.Descriptor instead.
func (*ConversationSummaryResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{16}
}

func (x *ConversationSummaryResponse) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

// Conversation Insert2Database4RAG messages
type SaveConversation4RAGRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Context         []*DialogueContext     `protobuf:"bytes,1,rep,name=context,proto3" json:"context,omitempty"`
	SpeakerId       string                 `protobuf:"bytes,2,opt,name=speaker_id,json=speakerId,proto3" json:"speaker_id,omitempty"`                   // 说话人id
	SpeakerName     string                 `protobuf:"bytes,3,opt,name=speaker_name,json=speakerName,proto3" json:"speaker_name,omitempty"`             // 说话人名称
	SpeakerNickname string                 `protobuf:"bytes,4,opt,name=speaker_nickname,json=speakerNickname,proto3" json:"speaker_nickname,omitempty"` // 说话人称呼
	UserId          string                 `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                            // 用户id，用于rag知识库存储和查询
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SaveConversation4RAGRequest) Reset() {
	*x = SaveConversation4RAGRequest{}
	mi := &file_opus_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveConversation4RAGRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveConversation4RAGRequest) ProtoMessage() {}

func (x *SaveConversation4RAGRequest) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveConversation4RAGRequest.ProtoReflect.Descriptor instead.
func (*SaveConversation4RAGRequest) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{17}
}

func (x *SaveConversation4RAGRequest) GetContext() []*DialogueContext {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *SaveConversation4RAGRequest) GetSpeakerId() string {
	if x != nil {
		return x.SpeakerId
	}
	return ""
}

func (x *SaveConversation4RAGRequest) GetSpeakerName() string {
	if x != nil {
		return x.SpeakerName
	}
	return ""
}

func (x *SaveConversation4RAGRequest) GetSpeakerNickname() string {
	if x != nil {
		return x.SpeakerNickname
	}
	return ""
}

func (x *SaveConversation4RAGRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type SaveConversation4RAGResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveConversation4RAGResponse) Reset() {
	*x = SaveConversation4RAGResponse{}
	mi := &file_opus_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveConversation4RAGResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveConversation4RAGResponse) ProtoMessage() {}

func (x *SaveConversation4RAGResponse) ProtoReflect() protoreflect.Message {
	mi := &file_opus_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveConversation4RAGResponse.ProtoReflect.Descriptor instead.
func (*SaveConversation4RAGResponse) Descriptor() ([]byte, []int) {
	return file_opus_proto_rawDescGZIP(), []int{18}
}

func (x *SaveConversation4RAGResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_opus_proto protoreflect.FileDescriptor

var file_opus_proto_rawDesc = string([]byte{
	0x0a, 0x0a, 0x6f, 0x70, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x70, 0x6f,
	0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x22, 0x2c, 0x0a, 0x0b, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x0c, 0x50, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0xbd, 0x01, 0x0a, 0x12, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0d,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a,
	0x0a, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x48, 0x00, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x44, 0x61, 0x74, 0x61, 0x12, 0x36,
	0x0a, 0x16, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x14, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x0b, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74,
	0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e,
	0x74, 0x52, 0x0b, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x3b,
	0x0a, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0b,
	0x61, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x0a,
	0x56, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70,
	0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x02, 0x52, 0x06, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x22, 0x89, 0x01, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9b, 0x01, 0x0a,
	0x09, 0x41, 0x73, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65,
	0x63, 0x6f, 0x67, 0x6e, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x7a, 0x65, 0x64, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e,
	0x74, 0x5f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x02, 0x52, 0x10,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x22, 0x97, 0x01, 0x0a, 0x18, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x73, 0x72, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x6f,
	0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x41, 0x73, 0x72, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x48, 0x00, 0x52, 0x09, 0x61, 0x73, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x36, 0x0a, 0x16, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x14, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa5, 0x04, 0x0a, 0x0f, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6f, 0x74, 0x61,
	0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6c, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6c, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x61,
	0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x73,
	0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x3b, 0x0a, 0x0c, 0x64, 0x65, 0x69, 0x76, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x44, 0x65, 0x69, 0x76, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0b, 0x64, 0x65, 0x69, 0x76, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x09,
	0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x80, 0x01, 0x0a,
	0x0f, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x20, 0x0a,
	0x0c, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x22,
	0x57, 0x0a, 0x0b, 0x44, 0x65, 0x69, 0x76, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x67, 0x22, 0x2e, 0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x10, 0x44, 0x69, 0x61,
	0x6c, 0x6f, 0x67, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x61, 0x75, 0x64, 0x69,
	0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00,
	0x52, 0x0b, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x3d, 0x0a,
	0x0c, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52,
	0x0b, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x09, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xf3, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x36, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x44,
	0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x70, 0x65,
	0x61, 0x6b, 0x65, 0x72, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x13,
	0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x70, 0x65, 0x61, 0x6b,
	0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3d, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x54, 0x0a, 0x1a,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6f,
	0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67,
	0x75, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x22, 0x37, 0x0a, 0x1b, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x22, 0xdb, 0x01, 0x0a, 0x1b,
	0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x34, 0x52, 0x41, 0x47, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x44, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x1c, 0x53, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x34, 0x52, 0x41,
	0x47, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x32, 0xb8, 0x04, 0x0a, 0x11, 0x50, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x43, 0x68,
	0x61, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x50, 0x69, 0x6e,
	0x67, 0x12, 0x18, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x6f,
	0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x16, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x25, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x6f, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x30, 0x01, 0x12, 0x54, 0x0a, 0x13,
	0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1d, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x30, 0x01, 0x12, 0x53, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x12, 0x20, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x13, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x27,
	0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f,
	0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6b, 0x0a, 0x14, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x34, 0x52, 0x41, 0x47, 0x12, 0x28, 0x2e, 0x70, 0x6f, 0x74, 0x61,
	0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x34, 0x52, 0x41, 0x47, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x70, 0x6f, 0x74, 0x61, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x34, 0x52, 0x41, 0x47, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x08,
	0x5a, 0x06, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_opus_proto_rawDescOnce sync.Once
	file_opus_proto_rawDescData []byte
)

func file_opus_proto_rawDescGZIP() []byte {
	file_opus_proto_rawDescOnce.Do(func() {
		file_opus_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_opus_proto_rawDesc), len(file_opus_proto_rawDesc)))
	})
	return file_opus_proto_rawDescData
}

var file_opus_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_opus_proto_goTypes = []any{
	(*PingRequest)(nil),                  // 0: potato_chat.PingRequest
	(*PingResponse)(nil),                 // 1: potato_chat.PingResponse
	(*AudioStreamRequest)(nil),           // 2: potato_chat.AudioStreamRequest
	(*InitialConfig)(nil),                // 3: potato_chat.InitialConfig
	(*Voiceprint)(nil),                   // 4: potato_chat.Voiceprint
	(*AudioParams)(nil),                  // 5: potato_chat.AudioParams
	(*AsrResult)(nil),                    // 6: potato_chat.AsrResult
	(*AudioRecognitionResponse)(nil),     // 7: potato_chat.AudioRecognitionResponse
	(*DialogueRequest)(nil),              // 8: potato_chat.DialogueRequest
	(*DialogueContext)(nil),              // 9: potato_chat.DialogueContext
	(*DeivceState)(nil),                  // 10: potato_chat.DeivceState
	(*RoleList)(nil),                     // 11: potato_chat.RoleList
	(*DialogueResponse)(nil),             // 12: potato_chat.DialogueResponse
	(*UpdateMemoryRequest)(nil),          // 13: potato_chat.UpdateMemoryRequest
	(*UpdateMemoryResponse)(nil),         // 14: potato_chat.UpdateMemoryResponse
	(*ConversationSummaryRequest)(nil),   // 15: potato_chat.ConversationSummaryRequest
	(*ConversationSummaryResponse)(nil),  // 16: potato_chat.ConversationSummaryResponse
	(*SaveConversation4RAGRequest)(nil),  // 17: potato_chat.SaveConversation4RAGRequest
	(*SaveConversation4RAGResponse)(nil), // 18: potato_chat.SaveConversation4RAGResponse
}
var file_opus_proto_depIdxs = []int32{
	3,  // 0: potato_chat.AudioStreamRequest.initial_config:type_name -> potato_chat.InitialConfig
	4,  // 1: potato_chat.InitialConfig.voiceprints:type_name -> potato_chat.Voiceprint
	5,  // 2: potato_chat.InitialConfig.audio_params:type_name -> potato_chat.AudioParams
	6,  // 3: potato_chat.AudioRecognitionResponse.asr_result:type_name -> potato_chat.AsrResult
	9,  // 4: potato_chat.DialogueRequest.context:type_name -> potato_chat.DialogueContext
	10, // 5: potato_chat.DialogueRequest.deivce_state:type_name -> potato_chat.DeivceState
	11, // 6: potato_chat.DialogueRequest.role_list:type_name -> potato_chat.RoleList
	5,  // 7: potato_chat.DialogueResponse.audio_params:type_name -> potato_chat.AudioParams
	9,  // 8: potato_chat.UpdateMemoryRequest.context:type_name -> potato_chat.DialogueContext
	9,  // 9: potato_chat.ConversationSummaryRequest.context:type_name -> potato_chat.DialogueContext
	9,  // 10: potato_chat.SaveConversation4RAGRequest.context:type_name -> potato_chat.DialogueContext
	0,  // 11: potato_chat.PotatoChatService.Ping:input_type -> potato_chat.PingRequest
	2,  // 12: potato_chat.PotatoChatService.AudioStreamRecognition:input_type -> potato_chat.AudioStreamRequest
	8,  // 13: potato_chat.PotatoChatService.DialogueInteraction:input_type -> potato_chat.DialogueRequest
	13, // 14: potato_chat.PotatoChatService.UpdateMemory:input_type -> potato_chat.UpdateMemoryRequest
	15, // 15: potato_chat.PotatoChatService.ConversationSummary:input_type -> potato_chat.ConversationSummaryRequest
	17, // 16: potato_chat.PotatoChatService.SaveConversation4RAG:input_type -> potato_chat.SaveConversation4RAGRequest
	1,  // 17: potato_chat.PotatoChatService.Ping:output_type -> potato_chat.PingResponse
	7,  // 18: potato_chat.PotatoChatService.AudioStreamRecognition:output_type -> potato_chat.AudioRecognitionResponse
	12, // 19: potato_chat.PotatoChatService.DialogueInteraction:output_type -> potato_chat.DialogueResponse
	14, // 20: potato_chat.PotatoChatService.UpdateMemory:output_type -> potato_chat.UpdateMemoryResponse
	16, // 21: potato_chat.PotatoChatService.ConversationSummary:output_type -> potato_chat.ConversationSummaryResponse
	18, // 22: potato_chat.PotatoChatService.SaveConversation4RAG:output_type -> potato_chat.SaveConversation4RAGResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_opus_proto_init() }
func file_opus_proto_init() {
	if File_opus_proto != nil {
		return
	}
	file_opus_proto_msgTypes[2].OneofWrappers = []any{
		(*AudioStreamRequest_InitialConfig)(nil),
		(*AudioStreamRequest_AudioData)(nil),
		(*AudioStreamRequest_ClientControlMessage)(nil),
	}
	file_opus_proto_msgTypes[7].OneofWrappers = []any{
		(*AudioRecognitionResponse_AsrResult)(nil),
		(*AudioRecognitionResponse_ServerControlMessage)(nil),
	}
	file_opus_proto_msgTypes[12].OneofWrappers = []any{
		(*DialogueResponse_ControlMessage)(nil),
		(*DialogueResponse_AudioStream)(nil),
		(*DialogueResponse_AudioParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_opus_proto_rawDesc), len(file_opus_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_opus_proto_goTypes,
		DependencyIndexes: file_opus_proto_depIdxs,
		MessageInfos:      file_opus_proto_msgTypes,
	}.Build()
	File_opus_proto = out.File
	file_opus_proto_goTypes = nil
	file_opus_proto_depIdxs = nil
}
