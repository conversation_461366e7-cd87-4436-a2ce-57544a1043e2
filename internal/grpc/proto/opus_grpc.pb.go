// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: opus.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PotatoChatService_Ping_FullMethodName                   = "/potato_chat.PotatoChatService/Ping"
	PotatoChatService_AudioStreamRecognition_FullMethodName = "/potato_chat.PotatoChatService/AudioStreamRecognition"
	PotatoChatService_DialogueInteraction_FullMethodName    = "/potato_chat.PotatoChatService/DialogueInteraction"
	PotatoChatService_UpdateMemory_FullMethodName           = "/potato_chat.PotatoChatService/UpdateMemory"
	PotatoChatService_ConversationSummary_FullMethodName    = "/potato_chat.PotatoChatService/ConversationSummary"
	PotatoChatService_SaveConversation4RAG_FullMethodName   = "/potato_chat.PotatoChatService/SaveConversation4RAG"
)

// PotatoChatServiceClient is the client API for PotatoChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service definition
type PotatoChatServiceClient interface {
	// Ping API (Request-Response)
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error)
	// Audio Stream Recognition API (Bidirectional Streaming)
	AudioStreamRecognition(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[AudioStreamRequest, AudioRecognitionResponse], error)
	// Dialogue Interaction API (Request-Streaming Response)
	DialogueInteraction(ctx context.Context, in *DialogueRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DialogueResponse], error)
	// Update Memory API (Request-Response)
	UpdateMemory(ctx context.Context, in *UpdateMemoryRequest, opts ...grpc.CallOption) (*UpdateMemoryResponse, error)
	// Conversation Summary API (Request-Response)
	ConversationSummary(ctx context.Context, in *ConversationSummaryRequest, opts ...grpc.CallOption) (*ConversationSummaryResponse, error)
	// Conversation Insert2Database4RAG API (Request-Response)
	SaveConversation4RAG(ctx context.Context, in *SaveConversation4RAGRequest, opts ...grpc.CallOption) (*SaveConversation4RAGResponse, error)
}

type potatoChatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPotatoChatServiceClient(cc grpc.ClientConnInterface) PotatoChatServiceClient {
	return &potatoChatServiceClient{cc}
}

func (c *potatoChatServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PingResponse)
	err := c.cc.Invoke(ctx, PotatoChatService_Ping_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *potatoChatServiceClient) AudioStreamRecognition(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[AudioStreamRequest, AudioRecognitionResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &PotatoChatService_ServiceDesc.Streams[0], PotatoChatService_AudioStreamRecognition_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[AudioStreamRequest, AudioRecognitionResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PotatoChatService_AudioStreamRecognitionClient = grpc.BidiStreamingClient[AudioStreamRequest, AudioRecognitionResponse]

func (c *potatoChatServiceClient) DialogueInteraction(ctx context.Context, in *DialogueRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DialogueResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &PotatoChatService_ServiceDesc.Streams[1], PotatoChatService_DialogueInteraction_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[DialogueRequest, DialogueResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PotatoChatService_DialogueInteractionClient = grpc.ServerStreamingClient[DialogueResponse]

func (c *potatoChatServiceClient) UpdateMemory(ctx context.Context, in *UpdateMemoryRequest, opts ...grpc.CallOption) (*UpdateMemoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateMemoryResponse)
	err := c.cc.Invoke(ctx, PotatoChatService_UpdateMemory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *potatoChatServiceClient) ConversationSummary(ctx context.Context, in *ConversationSummaryRequest, opts ...grpc.CallOption) (*ConversationSummaryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConversationSummaryResponse)
	err := c.cc.Invoke(ctx, PotatoChatService_ConversationSummary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *potatoChatServiceClient) SaveConversation4RAG(ctx context.Context, in *SaveConversation4RAGRequest, opts ...grpc.CallOption) (*SaveConversation4RAGResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveConversation4RAGResponse)
	err := c.cc.Invoke(ctx, PotatoChatService_SaveConversation4RAG_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PotatoChatServiceServer is the server API for PotatoChatService service.
// All implementations must embed UnimplementedPotatoChatServiceServer
// for forward compatibility.
//
// Service definition
type PotatoChatServiceServer interface {
	// Ping API (Request-Response)
	Ping(context.Context, *PingRequest) (*PingResponse, error)
	// Audio Stream Recognition API (Bidirectional Streaming)
	AudioStreamRecognition(grpc.BidiStreamingServer[AudioStreamRequest, AudioRecognitionResponse]) error
	// Dialogue Interaction API (Request-Streaming Response)
	DialogueInteraction(*DialogueRequest, grpc.ServerStreamingServer[DialogueResponse]) error
	// Update Memory API (Request-Response)
	UpdateMemory(context.Context, *UpdateMemoryRequest) (*UpdateMemoryResponse, error)
	// Conversation Summary API (Request-Response)
	ConversationSummary(context.Context, *ConversationSummaryRequest) (*ConversationSummaryResponse, error)
	// Conversation Insert2Database4RAG API (Request-Response)
	SaveConversation4RAG(context.Context, *SaveConversation4RAGRequest) (*SaveConversation4RAGResponse, error)
	mustEmbedUnimplementedPotatoChatServiceServer()
}

// UnimplementedPotatoChatServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPotatoChatServiceServer struct{}

func (UnimplementedPotatoChatServiceServer) Ping(context.Context, *PingRequest) (*PingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedPotatoChatServiceServer) AudioStreamRecognition(grpc.BidiStreamingServer[AudioStreamRequest, AudioRecognitionResponse]) error {
	return status.Errorf(codes.Unimplemented, "method AudioStreamRecognition not implemented")
}
func (UnimplementedPotatoChatServiceServer) DialogueInteraction(*DialogueRequest, grpc.ServerStreamingServer[DialogueResponse]) error {
	return status.Errorf(codes.Unimplemented, "method DialogueInteraction not implemented")
}
func (UnimplementedPotatoChatServiceServer) UpdateMemory(context.Context, *UpdateMemoryRequest) (*UpdateMemoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMemory not implemented")
}
func (UnimplementedPotatoChatServiceServer) ConversationSummary(context.Context, *ConversationSummaryRequest) (*ConversationSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConversationSummary not implemented")
}
func (UnimplementedPotatoChatServiceServer) SaveConversation4RAG(context.Context, *SaveConversation4RAGRequest) (*SaveConversation4RAGResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveConversation4RAG not implemented")
}
func (UnimplementedPotatoChatServiceServer) mustEmbedUnimplementedPotatoChatServiceServer() {}
func (UnimplementedPotatoChatServiceServer) testEmbeddedByValue()                           {}

// UnsafePotatoChatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PotatoChatServiceServer will
// result in compilation errors.
type UnsafePotatoChatServiceServer interface {
	mustEmbedUnimplementedPotatoChatServiceServer()
}

func RegisterPotatoChatServiceServer(s grpc.ServiceRegistrar, srv PotatoChatServiceServer) {
	// If the following call pancis, it indicates UnimplementedPotatoChatServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PotatoChatService_ServiceDesc, srv)
}

func _PotatoChatService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PotatoChatServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PotatoChatService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PotatoChatServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PotatoChatService_AudioStreamRecognition_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PotatoChatServiceServer).AudioStreamRecognition(&grpc.GenericServerStream[AudioStreamRequest, AudioRecognitionResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PotatoChatService_AudioStreamRecognitionServer = grpc.BidiStreamingServer[AudioStreamRequest, AudioRecognitionResponse]

func _PotatoChatService_DialogueInteraction_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DialogueRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PotatoChatServiceServer).DialogueInteraction(m, &grpc.GenericServerStream[DialogueRequest, DialogueResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type PotatoChatService_DialogueInteractionServer = grpc.ServerStreamingServer[DialogueResponse]

func _PotatoChatService_UpdateMemory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMemoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PotatoChatServiceServer).UpdateMemory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PotatoChatService_UpdateMemory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PotatoChatServiceServer).UpdateMemory(ctx, req.(*UpdateMemoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PotatoChatService_ConversationSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversationSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PotatoChatServiceServer).ConversationSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PotatoChatService_ConversationSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PotatoChatServiceServer).ConversationSummary(ctx, req.(*ConversationSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PotatoChatService_SaveConversation4RAG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveConversation4RAGRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PotatoChatServiceServer).SaveConversation4RAG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PotatoChatService_SaveConversation4RAG_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PotatoChatServiceServer).SaveConversation4RAG(ctx, req.(*SaveConversation4RAGRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PotatoChatService_ServiceDesc is the grpc.ServiceDesc for PotatoChatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PotatoChatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "potato_chat.PotatoChatService",
	HandlerType: (*PotatoChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _PotatoChatService_Ping_Handler,
		},
		{
			MethodName: "UpdateMemory",
			Handler:    _PotatoChatService_UpdateMemory_Handler,
		},
		{
			MethodName: "ConversationSummary",
			Handler:    _PotatoChatService_ConversationSummary_Handler,
		},
		{
			MethodName: "SaveConversation4RAG",
			Handler:    _PotatoChatService_SaveConversation4RAG_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "AudioStreamRecognition",
			Handler:       _PotatoChatService_AudioStreamRecognition_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "DialogueInteraction",
			Handler:       _PotatoChatService_DialogueInteraction_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "opus.proto",
}
