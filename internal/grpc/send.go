package grpc

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"github.com/uozi-tech/cosy/logger"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (c *Client) GrpcSendAudioConfig(pam *proto.AudioParams, voiceprints []*proto.Voiceprint) error {
	stream := c.audioStream
	initialConfig := &proto.AudioStreamRequest{
		Request: &proto.AudioStreamRequest_InitialConfig{
			InitialConfig: &proto.InitialConfig{
				AudioParams: pam,
				Voiceprints: voiceprints,
			},
		},
	}
	err := stream.Send(initialConfig)
	if err != nil {
		return fmt.Errorf("发送初始配置失败: %w", err)
	}
	return nil
}

func (c *Client) GrpcSendAudioControl(msg string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	req := &proto.AudioStreamRequest{
		Request: &proto.AudioStreamRequest_ClientControlMessage{
			ClientControlMessage: msg,
		},
	}
	// 带重试机制的发送
	for i := 0; i < maxRetries; i++ {
		err := c.audioStream.Send(req)
		if err == nil {
			logger.Debug("成功发送%s控制信号", msg)
			return nil
		} else {
			logger.Warn("%s信号发送失败(尝试 %d/%d): %v", msg,
				i+1, maxRetries, err)
		}

		// 针对不可恢复错误提前返回
		if status.Code(err) == codes.Canceled ||
			status.Code(err) == codes.Unavailable {
			break
		}

		time.Sleep(reconnectDelay)
	}
	logger.Debug("GrpcSendAudioOver执行,req:", req.String(), "c.audioStream:", c.audioStream)
	return fmt.Errorf("FINISH信号发送失败")
}
func (c *Client) SendAudioData(audio []byte) error {

	select {
	case c.audioDataChan <- audio:
		// fmt.Println("音频数据已发送到通道")
		return nil
	case <-time.After(500 * time.Millisecond):
		return errors.New("音频通道阻塞超时")
	case <-c.ctx.Done():
		return errors.New("上下文已取消")
	}
}
func (c *Client) sendAudioWorker() {
	defer c.wg.Done()

	for {
		select {
		case audio, ok := <-c.audioDataChan:
			// logger.Debug("audioDataChan is doing")
			if !ok {
				continue
			}
			// logger.Debug("audio", len(audio))
			req := &proto.AudioStreamRequest{
				Request: &proto.AudioStreamRequest_AudioData{
					AudioData: audio,
				},
			}

			if err := c.sendWithRetry(req); err != nil {
				logger.Error("最终发送失败: %v", err)
				return
			}

		case <-c.ctx.Done():
			logger.Debug("发送协程终止")
			return
		}
	}
}
func (c *Client) sendWithRetry(req *proto.AudioStreamRequest) error {
	for i := 0; i < maxRetries; i++ {
		c.mu.Lock()
		err := c.audioStream.Send(req)
		c.mu.Unlock()

		if err == nil {
			return nil
		}
	}
	return fmt.Errorf("超过最大重试次数(%d)", maxRetries)
}

// TODO
func (c *Client) sendToDialogueInteraction(sessionCtx *SessionContext) {

	// 创建对话请求
	req := c.GetDialogueRequest(sessionCtx)
	logger.Debug("sendToDialogueInteraction.req", req)
	// 发送请求并获取响应流
	ctx, cancel := context.WithTimeout(context.Background(), GrpcAudioTime)
	defer cancel()
	logger.Info("根据ASR请求交互接口")
	stream, err := c.potatoChatClient.DialogueInteraction(ctx, req)
	if err != nil {
		logger.Error("DialogueInteraction请求失败:", err)
		return
	}
	c.dialogStream = stream
	// 处理响应流
	// 立即进入handler的for循环中接受语音
	dialogue_handler, ok := GetHandler("dialogue_interaction")
	if ok {
		dialogue_handler(c)
	}

	// 用于打断时告诉硬件tts_stop
	// handler, ok := device.GetHandler("tts_stop")
	// if ok {
	// 	handler(sessionCtx.WebSocketFrame)
	// }
	logger.Debug("sendToDialogueInteraction结束")
	// 关闭gRPC客户端
	// c.Close()
}
