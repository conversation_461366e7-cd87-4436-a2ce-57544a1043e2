syntax = "proto3";

package potato_chat;
option go_package = "/proto";  // 关键修改
// protoc --go_out=. --go-grpc_out=. opus.proto  在当前目录生成proto的代码

// Service definition
service PotatoChatService {
  // Ping API (Request-Response)
  rpc Ping(PingRequest) returns (PingResponse);
  
  // Audio Stream Recognition API (Bidirectional Streaming)
  rpc AudioStreamRecognition(stream AudioStreamRequest) returns (stream AudioRecognitionResponse);
  
  // Dialogue Interaction API (Request-Streaming Response)
  rpc DialogueInteraction(DialogueRequest) returns (stream DialogueResponse);
  
  // Update Memory API (Request-Response)
  rpc UpdateMemory(UpdateMemoryRequest) returns (UpdateMemoryResponse);

  // Conversation Summary API (Request-Response)
  rpc ConversationSummary(ConversationSummaryRequest) returns (ConversationSummaryResponse);

  // Conversation Insert2Database4RAG API (Request-Response)
  rpc SaveConversation4RAG(SaveConversation4RAGRequest) returns (SaveConversation4RAGResponse);
}

// Ping API messages
message PingRequest {
  string session_id = 1;
}

message PingResponse {
  bool success = 1;
}

// Audio Stream Recognition messages
message AudioStreamRequest {
  oneof request {
    InitialConfig initial_config = 1;
    bytes audio_data = 2;  // OPUS audio stream
    string client_control_message = 3;
  }
}

message InitialConfig {
  repeated Voiceprint voiceprints = 1;
  AudioParams audio_params = 2;
}

message Voiceprint {
  string id = 1;
  string speaker_name = 2;      // 说话人名称
  string speaker_nickname = 3;  // 说话人称呼
  repeated float vector = 4;    // 特征向量是一个256维的浮点数组
}

message AudioParams {
  string format = 1;  // e.g., "opus"
  int32 sample_rate = 2;  // e.g., 16000
  int32 channels = 3;  // e.g., 1
  int32 frame_duration = 4;  // e.g., 60
}

// ASR Result structure
message AsrResult {
string recognized_text = 1;
repeated float voiceprint_vector = 2;
string speaker_id = 3;
bool is_final = 4;
}

message AudioRecognitionResponse {
  oneof response {
    AsrResult asr_result = 1;
    string server_control_message = 2;
  }
}

// Dialogue Interaction messages
message DialogueRequest {
  repeated DialogueContext context = 1;
  string role_name = 2;         // 角色名称
  string role_definition = 3;   // 角色定义
  string memory = 4;            // 短期记忆
  string voice_type = 5;        // 角色音色
  string llm_type = 6;          // 角色模型
  string speaker_name = 7;      // 说话人名称
  string speaker_nickname = 8;  // 说话人称呼
  string speaker_description = 9;  // 说话人描述
  string user_id = 10;           // 用户id，用于rag知识库存储和查询
  string speaker_id = 11;         // 说话人id
  DeivceState deivce_state = 12;  // 设备状态
  repeated RoleList role_list = 13; // 用户角色列表
  string language = 14;             // 指定语言偏好，中文：zh；英文：en；未指定为空。
}

message DialogueContext {
  string role = 1;          // "system", "assistant", "tool", or "user"
  string content = 2;
  string tool_calls = 3;    // assistant专属，大模型给出的调用方法名称和相关参数
  string tool_call_id = 4;  // tool专属，工具调用的id
}

message DeivceState {
  int32 volume = 1;          // 当前设备音量
  int32 level = 2;           // 当前设备电量
  bool charging = 3;          // 当前设备是否在充电
}

message RoleList {
  int64 id = 1;               // 角色id
  string name = 2;            // 角色名称
}

message DialogueResponse {
  oneof content {
    string control_message = 1;  // TTS start, TTS text, TTS end, IoT control, etc.
    bytes audio_stream = 2;  // OPUS audio stream
    AudioParams audio_params = 3;
  }
}

// Update Memory messages
message UpdateMemoryRequest {
  repeated DialogueContext context = 1;
  string memory_content = 2;
  string speaker_name = 3;      // 说话人名称
  string speaker_nickname = 4;  // 说话人称呼
  string speaker_description = 5;  // 说话人描述
}

message UpdateMemoryResponse {
  string memory_content = 1;
} 


// Conversation Summary messages
message ConversationSummaryRequest{
  repeated DialogueContext context = 1;
}

message ConversationSummaryResponse{
  string summary = 1;
}

// Conversation Insert2Database4RAG messages
message SaveConversation4RAGRequest{
  repeated DialogueContext context = 1;
  string speaker_id = 2;      // 说话人id
  string speaker_name = 3;      // 说话人名称
  string speaker_nickname = 4;  // 说话人称呼
  string user_id = 5;           // 用户id，用于rag知识库存储和查询
}

message SaveConversation4RAGResponse{
  bool success = 1;
}