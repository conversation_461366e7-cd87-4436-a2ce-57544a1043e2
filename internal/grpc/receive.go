package grpc

import (
	"io"
	"time"

	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"github.com/uozi-tech/cosy/logger"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (c *Client) receiveAudioResponses() {
	defer c.wg.Done()
	for {
		// 创建上下文以检测取消信号
		select {
		case <-c.ctx.Done():
			logger.Debug("接收响应协程被终止")
			return
		default:
			resp, err := c.audioStream.Recv()
			if resp == nil {
				logger.Debug("接收协程终止")
				logger.Debug("receiveAudioResponses中,服务器响应为空")
				return
			}
			if err != nil {
				if err == io.EOF || status.Code(err) == codes.Canceled {
					logger.Debug("服务器关闭流")
					// c.Close()
					return
				}
				logger.Error("接收错误:", err)
				// 服务器状态异常尝试进行重连
				if status.Code(err) == codes.Unavailable {

					logger.Debug("status.Code(err) == codes.Unavailable")
				}
				time.Sleep(GrpcReConnectTimeout)
				// continue
			}
			// 使用异步处理响应，避免阻塞接收循环
			c.handleAsrResponse(resp)
		}
	}
}

var Time1 int64

// 处理服务器响应
// 修改后的 handleResponse 函数
// handleResponse 处理来自gRPC服务器的响应
func (c *Client) handleAsrResponse(resp *proto.AudioRecognitionResponse) {
	// 获取会话ID (使用c.sessionID)
	sessionID := c.sessionID
	logger.Debug("[handleResponse] 处理服务器响应")
	// 尝试从会话映射中获取WebSocket上下文
	ctxObj, ok := SessionMap.Load(sessionID)
	if !ok {
		logger.Warn("未找到会话上下文:", sessionID)
		return
	}

	sessionCtx, ok := ctxObj.(*SessionContext)
	if !ok {
		logger.Error("会话上下文类型转换失败")
		return
	}

	// 锁定会话上下文以防止并发写入
	sessionCtx.mutex.Lock()
	defer sessionCtx.mutex.Unlock()
	// 打断文本的返回
	if sessionCtx.GrpcClient.IsReturnWsPause {
		logger.Debug("打断文本的返回")
		sessionCtx.GrpcClient.IsReturnWsPause = false
		return
	}
	last_vector := make([]float32, 0)
	switch res := resp.Response.(type) {
	case *proto.AudioRecognitionResponse_AsrResult:
		// 处理语音识别结果
		result := res.AsrResult
		sessionCtx.SpeakerId = result.SpeakerId
		logger.Info("handleAsrResponse.sessionCtx.SpeakerId->", sessionCtx.SpeakerId)

		// 如果是最终结果且文本非空，自动发送到对话接口
		if result.RecognizedText != "" {
			handler, ok := device.GetHandler("stt")
			if !ok {
				logger.Error("[ASR] handler not found")
				return
			}
			sessionCtx.WebSocketFrame.Data.Type = "stt"
			sessionCtx.WebSocketFrame.Data.State = "sentence_start"
			sessionCtx.WebSocketFrame.Data.Text = result.RecognizedText
			handler(sessionCtx.WebSocketFrame)

			c.mu.Lock()
			c.Text += result.RecognizedText
			c.mu.Unlock()
			last_vector = result.VoiceprintVector
			// 说明本次结束
			if result.IsFinal {
				// 将信息存入数据库和存入缓存
				Time1 = time.Now().UnixMilli()
				c.triggerDialogueInteraction(sessionCtx, last_vector)
				logger.Debug("Time1", Time1)
				// 文本发送到grpc服务端
				c.sendToDialogueInteraction(sessionCtx)
				sessionCtx.GrpcClient.Text = ""
			} else {
				logger.Info("非最终结果:", sessionCtx.GrpcClient.Text)
			}
		}

	case *proto.AudioRecognitionResponse_ServerControlMessage:
		// 处理服务器控制消息
		logger.Debug("[控制] 服务器消息:", res.ServerControlMessage)
		if res.ServerControlMessage == "FINISH" {
			if c.Text != "" {
				c.triggerDialogueInteraction(sessionCtx, last_vector)
				c.sendToDialogueInteraction(sessionCtx)
			}
			sessionCtx.GrpcClient.Text = ""
		}
	}
}
