// ASR Result structure
message AsrResult {
  string recognized_text = 1;
  string voiceprint_vector = 2;
  string speaker_id = 3;
  bool is_final = 4;
}
当asr中 is_final为 true的时候,将全部的文本拼接起来去请求  DialogueInteraction 接口


<!-- grpc服务端返回isfinal为fasle的时候,返回grpc服务端该次的文本到硬件,拼凑grpc的文本,直到收到true,一起发给grpc服务端 -->

<!-- protoc --go_out=. --go-grpc_out=. opus.protos -->
// protoc --go_out=. --go-grpc_out=. opus.proto  在当前目录生成proto的代码
option go_package = "/proto";  // 关键修改


role:assistant content:""
role tool content:"xxx"
role assistant content:"xxx"
