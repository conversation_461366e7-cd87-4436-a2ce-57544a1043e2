package grpc

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/settings"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	maxRetries       = 3
	reconnectDelay   = 2 * time.Second
	audioChanBufSize = 10000
)

type SessionContext struct {
	WebSocketFrame    *device.WebSocketFrame
	Device            *model.Device
	Chat              *model.Chat
	UserChatMessageID uint64
	AIChatMessageID   uint64
	SpeakerId         string
	GrpcClient        *Client
	UserOpusFrames    [][]byte
	DeviceState       *model.DeviceState
	Agent             model.IntelligentAgent
	mutex             sync.Mutex
	AgentList         []*proto.RoleList
	LastActive        uint64
}

var conn *grpc.ClientConn
var SessionMap sync.Map // 全局线程安全存储
type Client struct {
	conn             *grpc.ClientConn
	potatoChatClient proto.PotatoChatServiceClient
	audioStream      proto.PotatoChatService_AudioStreamRecognitionClient
	dialogStream     proto.PotatoChatService_DialogueInteractionClient
	audioDataChan    chan []byte //缓冲通道，连接发送到grpc的通道
	wg               sync.WaitGroup
	ctx              context.Context
	cancel           context.CancelFunc
	mu               sync.Mutex
	sessionID        string
	IsReturnWsPause  bool
	Text             string                   //某一次对话的全部文本
	dialogContext    []*proto.DialogueContext //历史对话列表
	ttsMsg           TTSControlMsg
}

func GetNewClient() (*Client, error) {
	var err error
	conn, err = grpc.Dial(settings.GrpcSettings.Host+":"+settings.GrpcSettings.Port, grpc.WithInsecure())
	if err != nil {
		logger.Error("连接gRPC服务器失败: %v", err)
		return nil, err
	}
	GrpcClient := NewClient(conn)
	go GrpcClient.Run()
	time.Sleep(GrpcWaitAudioStreamInit)

	return GrpcClient, err
}
func NewClient(conn *grpc.ClientConn) *Client {

	sessionId := uuid.New().String()
	basectx := context.WithValue(context.Background(), "sessionid", sessionId)
	// 创建 metadata 并附加 sessionID
	md := metadata.Pairs("sessionid", sessionId) // 键名统一为小写
	basectx1 := metadata.NewOutgoingContext(basectx, md)
	ctx, cancel := context.WithCancel(basectx1)
	// go cleanupExpiredSessions(ctx)
	return &Client{
		conn:             conn,
		potatoChatClient: proto.NewPotatoChatServiceClient(conn),
		audioDataChan:    make(chan []byte, audioChanBufSize),
		ctx:              ctx,
		cancel:           cancel,
		sessionID:        sessionId,
		IsReturnWsPause:  false,
		Text:             "",
		dialogContext:    make([]*proto.DialogueContext, 0),
	}
}

func Ping(frame *device.WebSocketFrame) (*Client, error) {
	GrpcClient, err := GetNewClient()
	if err != nil {
		logger.Error("GetNewClient err:", err)
		return nil, err
	}
	if GrpcClient == nil {
		return nil, err
	}
	res, err := GrpcClient.Ping("1")
	if !res {
		logger.Error("Ping Grpc服务器失败 %s", err)
		time.Sleep(1 * time.Second)
		// 通知客户端连接失败
		handler, ok := device.GetHandler("tts_error")
		if ok {
			frame.Data.Text = "Ping Grpc-Server失败"
			handler(frame)
		}
		return GrpcClient, err
	}
	return GrpcClient, err
}

func (c *Client) Run() error {
	// logger.Debug("Run前 ->", c.audioStream)
	stream, err := c.InitAudioStream()
	// logger.Debug("Run后 ->", c.audioStream)
	if err != nil {
		logger.Error("初始化音频流失败: %v", err)
		return err
	}
	if stream == nil {
		logger.Error("初始化音频流失败,发现是空的: %v", err)
		return err
	}
	//  保持运行
	<-c.ctx.Done() // Directly receive from the channel
	logger.Info("客户端正常退出")
	return nil
}

func (c *Client) InitAudioStream() (proto.PotatoChatService_AudioStreamRecognitionClient, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	var stream proto.PotatoChatService_AudioStreamRecognitionClient
	var err error

	// TODO,ctx中携带seesionid
	stream, err = c.potatoChatClient.AudioStreamRecognition(c.ctx, grpc.WaitForReady(true))
	if err != nil {
		return stream, fmt.Errorf("无法建立音频流: %w", err)
	}
	c.audioStream = stream

	// 启动协程处理发送和接收,并等待send和receive完成
	c.wg.Add(2) //增加协程管理的计数器,方便在close的时候计数器减到0,形成正确关闭,否则close的时候,计数器已经为0了,导致无法正确关闭这两个协程
	go c.sendAudioWorker()
	go c.receiveAudioResponses()

	logger.Debug("InitAudioStream执行结束")
	return stream, nil
}
func (c *Client) Ping(sessionID string) (bool, error) {
	req := &proto.PingRequest{
		SessionId: sessionID,
	}

	ctx, cancel := context.WithTimeout(context.Background(), GrpcReConnectTimeout)
	defer cancel()
	var resp *proto.PingResponse
	var err error
	for i := 0; i < maxRetries; i++ {
		resp, err = c.potatoChatClient.Ping(ctx, req)
		if err == nil {
			break
		}
		logger.Error("流创建失败（尝试 %d/%d）: %v", i+1, maxRetries, err)
	}

	if err != nil {
		return false, fmt.Errorf("ping失败: %w", err)
	}
	return resp.Success, nil
}

func (c *Client) Close() {
	// 1. 首先取消上下文，通知所有协程停止
	c.cancel()

	// 2. 关闭音频数据通道
	close(c.audioDataChan)

	// 3. 等待所有协程完成
	c.wg.Wait()

	// 4. 关闭音频流
	if c.audioStream != nil {
		logger.Info("关闭音频流")
		if err := c.audioStream.CloseSend(); err != nil {
			logger.Debug("关闭音频流失败: %v", err)
		}
	}
	// 关闭交互流
	if c.dialogStream != nil {
		logger.Info("关闭交互流")
		if err := c.dialogStream.CloseSend(); err != nil {
			logger.Debug("关闭音频流失败: %v", err)
		}
	}

	// 5. 关闭gRPC连接
	if c.conn != nil {
		if err := c.conn.Close(); err != nil {
			logger.Debug("关闭连接失败: %v", err)
		}
	}

	// 6. 清理会话相关数据
	c.Text = ""
	c.dialogContext = make([]*proto.DialogueContext, 0)

	c.IsReturnWsPause = false

	logger.Debug("gRPC客户端已完全关闭")
}
