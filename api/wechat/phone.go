package wechat

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/wechat"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func SetUserPhone(c *gin.Context) {
	var json struct {
		Code string `json:"code"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	user := api.CurrentUser(c)

	phone, err := wechat.GetPhoneNumber(json.Code)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	u := query.User

	_, err = u.Where(u.ID.Eq(user.ID)).Updates(&model.User{
		Phone: phone,
	})
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"phone": phone,
	})
}
