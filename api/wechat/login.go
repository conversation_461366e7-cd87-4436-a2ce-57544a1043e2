package wechat

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/wechat"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"git.uozi.org/uozi/potato-api/settings"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

type Result struct {
	SessionKey string `json:"session_key,omitempty"`
	OpenID     string `json:"openid,omitempty"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

func Login(c *gin.Context) {
	baseUrl := "https://api.weixin.qq.com/sns/jscode2session"

	u, err := url.Parse(baseUrl)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var data struct {
		Code string `json:"code" binding:"required"`
	}

	if !cosy.BindAndValid(c, &data) {
		return
	}

	q := u.Query()
	q.Add("appid", settings.WeChatSettings.AppID)
	q.Add("secret", settings.WeChatSettings.AppSecret)
	q.Add("js_code", data.Code)
	q.Add("grant_type", "authorization_code")

	u.RawQuery = q.Encode()

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	req.Header.Add("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		cosy.ErrHandler(c, wechat.ErrTryAgainLater)
		return
	}

	bytes, err := io.ReadAll(resp.Body)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var res Result
	err = json.Unmarshal(bytes, &res)

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if res.ErrCode != 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    res.ErrCode,
			"message": res.ErrMsg,
		})
		return
	}

	user := query.User

	userPtr, err := user.Where(user.WeChatOpenID.Eq(res.OpenID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, wechat.ErrUserNotFound)
			return
		}

		cosy.ErrHandler(c, err)
		return
	}

	err = userPtr.Updates(&model.User{
		WeChatSessionKey: res.SessionKey,
	})
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	logger.Info("[User Login]", userPtr.Name)

	userPtr.UpdateLastActive()

	token, err := api.GenerateToken(userPtr)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":  http.StatusOK,
		"token": token,
	})
}
