package role_template

import (
	"fmt"

	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitRoleTemplateRouter(g *gin.RouterGroup) {
	g.GET("/role_template_categories", GetRoleCategoryList)
	r := g.Group("/role_templates")
	{
		r.GET("/:id", GetRoleTemplate)
		r.GET("", GetRoleTemplateList)
	}
}

func GetRoleCategoryList(c *gin.Context) {
	cosy.Core[model.RoleCategory](c).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Order("order_id ASC")
		}).
		PagingList()
}

func GetRoleTemplate(c *gin.Context) {
	cosy.Core[model.RoleTemplate](c).
		SetPreloads("Img").
		ExecutedHook(func(c *cosy.Ctx[model.RoleTemplate]) {
			c.Model.PreloadRoleCategories()
		}).
		Get()
}

func GetRoleTemplateList(c *gin.Context) {
	cosy.Core[model.RoleTemplate](c).
		SetPreloads("Img").
		SetEqual("preset").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			categoryIds := c.QueryArray("role_category_ids[]")
			if len(categoryIds) == 0 {
				categoryIds = c.QueryArray("role_category_ids")
			}
			if len(categoryIds) > 0 {
				categoryIds = lo.Map(categoryIds, func(id string, _ int) string {
					return fmt.Sprintf("\"%s\"", id)
				})
				tx = tx.Where("JSON_CONTAINS(role_category_ids, ?)", categoryIds)
			}
			// preset 为 true 的排序在前面
			return tx.Order("preset DESC")
		}).
		SetScan(func(tx *gorm.DB) any {
			var roleTemplates model.RoleTemplateList
			tx.Find(&roleTemplates)
			roleTemplates.PreloadRoleCategories()
			return roleTemplates
		}).
		PagingList()
}
