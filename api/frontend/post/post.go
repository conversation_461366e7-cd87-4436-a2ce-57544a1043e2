package post

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func GetPostList(c *gin.Context) {
	cosy.Core[model.Post](c).
		SetPreloads("Banner").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("status = ?", model.PostStatusPublish)
		}).
		PagingList()
}

func GetPost(c *gin.Context) {
	cosy.Core[model.Post](c).
		SetPreloads("Banner").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("status = ?", model.PostStatusPublish)
		}).
		Get()
}
