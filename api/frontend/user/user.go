package user

import (
	"net/http"
	"time"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/redis"
)

type Code struct {
	Code string `json:"code" binding:"required"` // 字段名首字母大写
}

func BundleUser(c *gin.Context) {
	// 解析用户用户token，拿到userid
	// 拿到验证码，双双存入数据库
	user := api.CurrentUser(c)
	userid := user.ID
	var ver_code Code
	if err := c.ShouldBindJSON(&ver_code); err != nil {
		logger.Error("绑定失败:", err)
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "参数校验失败"})
		return
	}
	code := device.GenCodeRedisKey(cast.ToString(ver_code.Code))
	// 通过验证码在redis中获取mac
	logger.Debug("BundleUser.redis_key:", code)
	mac, err := redis.Get(code)
	logger.Debug("mac->", mac)
	if err != nil {
		logger.Error("get code by redis error", err)
		c.JSON(http.StatusOK, gin.H{
			"msg": "get code by redis error",
		})
		return
	}
	d := query.Device
	device, err := d.Where(d.MAC.Eq(mac)).First()
	if err != nil {
		logger.Error("用户输入验证码时，发现设备mac不存在", err)
	}
	logger.Debug("vercode->", ver_code)
	logger.Debug("userid->", userid)
	logger.Debug("device->", device)

	dt, err := query.DeviceType.Where(query.DeviceType.Identifier.Eq(device.Identifier)).Preload(query.DeviceType.DefaultRole).First()
	if err != nil {
		logger.Error("获取设备类型失败", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	logger.Debug("dt->", dt)
	logger.Debug("dt.identifier->", dt.Identifier)
	logger.Debug("device.Identifier->", device.Identifier)
	// 判断用户是否已经创建了智能体
	count, err := query.IntelligentAgent.Where(query.IntelligentAgent.UserID.Eq(userid)).Count()
	if err != nil {
		logger.Error("获取智能体数量失败", err)
		c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	agent := &model.IntelligentAgent{}
	if count == 0 {
		role := dt.DefaultRole
		// 如果没有智能体，创建一个
		agent = &model.IntelligentAgent{
			UserID:      userid,
			Name:        role.Name,
			Description: role.Description,
			ImgID:       role.ImgID,
			Prompt:      role.Prompt,
			VoiceType:   role.VoiceType,
			Language:    role.Language,
		}
		query.IntelligentAgent.Create(agent)
	} else {
		// 如果有智能体，获取第一个智能体
		agent, err = query.IntelligentAgent.Where(query.IntelligentAgent.UserID.Eq(userid)).First()
		if err != nil {
			logger.Error("获取智能体失败", err)
			c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
	}

	newdevice := &model.Device{
		Model:              model.Model{ID: device.ID},
		Code:               ver_code.Code,
		UserId:             userid,
		MAC:                mac,
		IntelligentAgentID: agent.ID,
		Identifier:         dt.Identifier,
	}
	d.Where(d.MAC.Eq(mac)).Save(newdevice)

	c.JSON(http.StatusOK, newdevice)
}

func UnBundleUser(c *gin.Context) {
	id := c.Param("id")

	updateFields := map[string]interface{}{
		"user_id":              0,
		"remark":               "",
		"code":                 "",
		"ota_enabled":          false, // 更符合布尔类型的存储方式
		"ota_channel":          0,
		"intelligent_agent_id": 0,
		"updated_at":           time.Now().Unix(),
	}
	db := cosy.UseDB()
	// 使用 GORM 的更新方法
	err := db.Model(&model.Device{}).
		Where("id = ?", id).
		Updates(updateFields).Error

	if err != nil {
		logger.Error("设备解绑失败:", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "设备解绑失败",
		})
		return
	}
	// db := cosy.UseDB()
	// err := db.Exec("UPDATE devices SET user_id = 0,remark = '',system_version = '',code = '',ota_enabled = 0,ota_channel = 0,intelligent_agent_id = 0,updated_at = ? WHERE id = ?", time.Now().Unix(), id).Error
	// if err != nil {
	// 	logger.Error("设备解绑失败:", err)
	// 	c.JSON(http.StatusBadRequest, gin.H{"error": "设备解绑失败"})
	// 	return
	// }

	c.JSON(http.StatusOK, gin.H{
		"msg": "设备解绑成功",
	})
}
