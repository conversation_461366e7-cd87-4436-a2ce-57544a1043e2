package chat

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func GetChat(c *gin.Context) {
	cosy.Core[model.Chat](c).
		SetPreloads("User", "Agent", "Device", "Messages").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			user := api.CurrentUser(c)
			return tx.Where("user_id", user.ID)
		}).
		Get()
}

func GetChatList(c *gin.Context) {
	cosy.Core[model.Chat](c).
		SetPreloads("User", "Agent", "Device").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			user := api.CurrentUser(c)
			return tx.Where("user_id", user.ID)
		}).
		PagingList()
}

func GetChatMessageList(c *gin.Context) {
	cosy.Core[model.ChatMessage](c).
		SetPreloads("Chat").
		GormScope(func(tx *gorm.DB) *gorm.DB {
			user := api.CurrentUser(c)
			return tx.Where("user_id", user.ID)
		}).
		PagingList()
}

func DeleteChat(c *gin.Context) {
	cosy.Core[model.Chat](c).
		GormScope(func(tx *gorm.DB) *gorm.DB {
			user := api.CurrentUser(c)
			return tx.Where("user_id", user.ID)
		}).
		Destroy()
}
