package voiceprint

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitVoiceprintRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.Voiceprint]("voiceprints")
	// 用户侧接口不需要 cosy.Guard

	c.CreateHook(func(c *cosy.Ctx[model.Voiceprint]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Voiceprint]) {
			// 在用户侧，使用当前用户ID覆盖
			ctx.Model.UserID = api.CurrentUser(ctx.Context).ID
		})
	})

	c.ModifyHook(func(c *cosy.Ctx[model.Voiceprint]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Voiceprint]) {
			// 确保只能修改自己的声纹
			ctx.GormScope(func(tx *gorm.DB) *gorm.DB {
				// 添加用户ID过滤，确保只能操作自己的声纹
				currentUserID := api.CurrentUser(ctx.Context).ID
				return tx.Where("user_id = ?", currentUserID)
			})
		})
	})

	c.BeforeGetList(func(c *gin.Context) {
		// 用于过滤只有当前用户的声纹
	})

	c.GetListHook(func(c *cosy.Ctx[model.Voiceprint]) {
		// 在获取列表时，添加用户ID条件
		currentUserID := api.CurrentUser(c.Context).ID
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("user_id = ?", currentUserID)
		})
	})

	c.DestroyHook(func(c *cosy.Ctx[model.Voiceprint]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Voiceprint]) {
			// 确保只能删除自己的声纹
			ctx.GormScope(func(tx *gorm.DB) *gorm.DB {
				currentUserID := api.CurrentUser(ctx.Context).ID
				return tx.Where("user_id = ?", currentUserID)
			})
		})
	})

	c.InitRouter(g)
}
