package device

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func GetDevice(c *gin.Context) {
	user := api.CurrentUser(c)
	cosy.Core[model.Device](c).
		SetPreloads("User", "IntelligentAgent", "DeviceType", "DeviceType.DefaultRole", "IntelligentAgent.Img").
		GormScope(
			func(tx *gorm.DB) *gorm.DB {
				return tx.Where("user_id = ?", user.ID)
			},
		).Get()
}

func GetDeviceList(c *gin.Context) {
	user := api.CurrentUser(c)
	cosy.Core[model.Device](c).
		SetPreloads("User", "IntelligentAgent", "DeviceType", "IntelligentAgent.Img").
		GormScope(
			func(tx *gorm.DB) *gorm.DB {
				return tx.Where("user_id = ?", user.ID)
			},
		).PagingList()
}

func ModifyDevice(c *gin.Context) {
	deviceId := cast.ToUint64(c.Param("id"))

	user := api.CurrentUser(c)

	d := query.Device
	device, err := d.Where(d.UserId.Eq(user.ID)).FirstByID(deviceId)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var json struct {
		Remark             string `json:"remark"`
		OTAEnabled         bool   `json:"ota_enabled"`
		IntelligentAgentID string `json:"intelligent_agent_id"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	ia := query.IntelligentAgent
	intelligentAgent, err := ia.Where(ia.UserID.Eq(user.ID)).
		FirstByID(cast.ToUint64(json.IntelligentAgentID))
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	db := cosy.UseDB()
	err = db.Model(device).Where("id = ?", device.ID).Updates(map[string]any{
		"remark":               json.Remark,
		"ota_enabled":          json.OTAEnabled,
		"intelligent_agent_id": intelligentAgent.ID,
	}).Error
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, device)
}
