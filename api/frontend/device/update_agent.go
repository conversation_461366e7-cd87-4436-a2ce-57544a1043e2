package device

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
)

func BatchUpdateDevicesIntelligentAgent(c *gin.Context) {
	var json struct {
		DeviceIds          []string `json:"device_ids"`
		IntelligentAgentId string   `json:"intelligent_agent_id"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	user := api.CurrentUser(c)

	ia := query.IntelligentAgent
	intelligentAgent, err := ia.Where(ia.UserID.Eq(user.ID)).FirstByID(cast.ToUint64(json.IntelligentAgentId))
	if err != nil {
		cosy.ErrHandler(c, errors.Wrap(err, "intelligent agent not found"))
		return
	}

	deviceIds := make([]uint64, 0)
	for _, deviceId := range json.DeviceIds {
		deviceIds = append(deviceIds, cast.ToUint64(deviceId))
	}

	d := query.Device
	d.Where(d.UserId.Eq(user.ID)).Where(d.ID.In(deviceIds...)).Pluck(d.ID, &deviceIds)

	if len(deviceIds) > 0 {
		_, err = d.Where(d.ID.In(deviceIds...)).Update(d.IntelligentAgentID, intelligentAgent.ID)
		if err != nil {
			cosy.ErrHandler(c, errors.Wrap(err, "update devices intelligent agent failed"))
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "ok",
	})
}
