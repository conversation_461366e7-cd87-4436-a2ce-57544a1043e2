package intelligent_agent

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitIntelligentAgentRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.IntelligentAgent]("intelligent_agents")

	c.<PERSON><PERSON><PERSON>(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.IntelligentAgent]) {
			ctx.Model.UserID = api.CurrentUser(ctx.Context).ID

			if ctx.Model.RoleTemplateID == 0 {
				return
			}

			db := cosy.UseDB()
			var agent model.IntelligentAgent
			db.Where(&model.IntelligentAgent{
				UserID:         ctx.Model.UserID,
				RoleTemplateID: ctx.Model.RoleTemplateID,
			}).Find(&agent)
			if agent.ID == 0 {
				return
			}

			ctx.JSON(http.StatusOK, agent)
			ctx.Abort()
		})
	})

	c.Modify<PERSON>ook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("user_id = ?", api.CurrentUser(c.Context).ID)
		})
	})

	c.GetListHook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("user_id = ?", api.CurrentUser(c.Context).ID)
		})
		c.SetPreloads("RoleTemplate.Img")
		c.SetScan(func(tx *gorm.DB) any {
			var agents model.IntelligentAgents
			tx.Find(&agents)
			agents.OverrideByRoleTemplate()
			return agents
		})
	})

	c.GetHook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.SetPreloads("RoleTemplate.Img")
		c.SetTransformer(func(m *model.IntelligentAgent) any {
			m.GetRole()
			return m
		})
	})

	c.DestroyHook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("user_id = ?", api.CurrentUser(c.Context).ID)
		})
	})

	c.InitRouter(g)
}
