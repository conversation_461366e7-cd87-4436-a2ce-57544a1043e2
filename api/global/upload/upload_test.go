package upload

import (
	"bytes"
	"encoding/json"
	"git.uozi.org/uozi/potato-api/internal/oss"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"git.uozi.org/uozi/potato-api/settings"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/router"
	"github.com/uozi-tech/cosy/sandbox"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
)

func TestUpload(t *testing.T) {
	sandbox.NewInstance("../../../app.testing.ini", "mysql").
		RegisterModels(model.Upload{}).Run(func(instance *sandbox.Instance) {
		r := router.GetEngine()

		r.POST("/upload", func(c *gin.Context) {
			c.Set("user", &model.User{
				Model: model.Model{
					ID: 1,
				},
				UserGroupID: 1,
			})
		}, MediaUpload)

		// Test case 1: trust category
		t.Run("trust category", func(t *testing.T) {
			w := httptest.NewRecorder()
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			_ = writer.WriteField("to", "abc")
			_ = writer.Close()
			req := httptest.NewRequest(http.MethodPost, "/upload", body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			r.ServeHTTP(w, req)
			resp := w.Result()
			defer resp.Body.Close()
			respBodyBytes, _ := io.ReadAll(resp.Body)

			assert.Equal(t, http.StatusNotAcceptable, resp.StatusCode)
			assert.Contains(t, string(respBodyBytes), "target category is not trusted")
		})

		// Test case 2: upload file
		t.Run("upload file", func(t *testing.T) {
			db := cosy.UseDB()
			query.Init(db)

			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			fileName := "test-" + uuid.NewString() + ".txt"
			part, err := writer.CreateFormFile("file", fileName)
			if err != nil {
				t.Error(err)
				return
			}
			file, err := os.CreateTemp("", fileName)
			if err != nil {
				t.Error(err)
				return
			}
			defer os.Remove(file.Name())

			_, err = io.Copy(part, file)
			if err != nil {
				t.Error(err)
				return
			}

			err = writer.Close()
			if err != nil {
				t.Error(err)
				return
			}

			req := httptest.NewRequest("POST", "/upload", body)
			req.Header.Set("Content-Type", writer.FormDataContentType())

			w := httptest.NewRecorder()
			router.GetEngine().ServeHTTP(w, req)
			resp := w.Result()
			defer resp.Body.Close()

			var respData struct {
				Path string `json:"path"`
			}

			respBytes, _ := io.ReadAll(resp.Body)

			_ = json.Unmarshal(respBytes, &respData)
			assert.Equal(t, http.StatusOK, resp.StatusCode)
			o, _ := oss.NewOSS()
			path := strings.TrimLeft(strings.ReplaceAll(respData.Path, settings.OssSettings.BaseUrl, ""), "/")
			logger.Debug(respData.Path)
			err = o.DeleteObject(path)
			assert.Nil(t, err)
		})
	})
}
