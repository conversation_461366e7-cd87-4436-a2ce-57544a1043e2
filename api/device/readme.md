<!-- 进入for后 -->

先发一个frame.Data.Type:TextMessage iot   type为iot
然后发一个type为listen，state为start，表示设备开始听主人讲话 ，
然后开始发frame.Data.Type为BinaryMessage， frame.Data是二进制的流
将流存入缓冲区，
收到然后发一个type为listen，state为stop时，将流汇入grpc

**websocket建立连接时，发送一段问候语**

1. **客户端 → 服务器**（握手）
   ```json
   {
     "type": "hello",
     "version": 1,
     "transport": "websocket",
     "audio_params": {
       "format": "opus",
       "sample_rate": 16000,
       "channels": 1,
       "frame_duration": 60
     }
   }
   ```

2. **服务器 → 客户端**（握手应答）
   ```json
   {
     "type": "hello",
     "transport": "websocket",
     "audio_params": {
       "sample_rate": 16000
     }
   }
   ```

4. **客户端 → 服务器**（开始监听）
   ```json
   {
     "session_id": "",
     "type": "listen",
     "state": "start",
     "mode": "mannul"
   }
   ```
   同时客户端开始发送二进制帧（Opus 数据）。
4. **客户端 → 服务器**（opus流）
5.  **客户端 → 服务器**（停止监听）
6. **服务器 → 客户端**（ASR 结果）
   ```json
   {
     "type": "stt",
     "text": "用户说的话"
   }
   ```
7. **服务器 → 客户端**（STT）text

8. **服务器 → 客户端**（TTS开始）opus
   ```json
   {
     "type": "tts",
     "state": "start"
   }
   ```
   接着服务器发送二进制音频帧给客户端播放。
9. **可能 服务器 → 客户端**（STT）text

10. **服务器 → 客户端**（TTS结束）opus
   ```json
   {
     "type": "tts",
     "state": "stop"
   }
   ```
   (传完6位验证码后立即开始)
二维码开始传输
   ```json
   {
     "type": "qrcode",
     "state": "start"
   }
   ```
  ```json
   {
     "type": "qrcode",
     "text": "JCQk/+rq6v8AAPHgDwAAAADx4A8AAB/4wZgPH/g//MGYDx/8P/zBuA8f/Dgc8fgzGAw4HPH4MxgMOBzwGDMYDDgc8BgzGAw4HP/mPxgMOBz/5j8YDD/8wAfzH/w//MAH8x/8H/jAB/Mf+AAAzmYzAAAAAM5mM4AA///P5jP/////z+cz////gDOfz4fz/4Axn88H8+OPz5w/HwPBn8+YPx4Dw4+PmD8+Az+AD5jM+fA/gA8YzPnwwZ8wAMz/88GfMADMf/M5/MBmDAf8OfzAZgwH/DgfwYY8fx84H8GGPP4POAABwAD8APngAfjA+YD54AP4gPmA/h/PmADnj/4fz5gAZ88AHP5n8AHwABz+Z/AB8P//wB9x+Hz//8AeM/h8AAHH/AO4cAAAz/gDGHAAAMf4A7hwP/zB+MP5/z/8wfjD+f84HM4AMAGPOBzOADABjzgc8H4wR4w4HPB+MOeMOBzwZgEAAzgc8GYDAAM4HPBmAQADP/zxhjDmAB/4+YYw5gAAAP4AzOf8AAD+AMzn/A=="
   }
   ```


二维码传输结束
   ```json
   {
     "type": "qrcode",
     "state": "stop"
   }
   ```