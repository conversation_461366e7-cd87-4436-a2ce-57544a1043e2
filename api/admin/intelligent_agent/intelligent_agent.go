package intelligent_agent

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitIntelligentAgentRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.IntelligentAgent]("intelligent_agents")
	api.CosyGuard(c, acl.IntelligentAgent)

	c.GetListHook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.SetPreloads("RoleTemplate.Img")
		c.SetScan(func(tx *gorm.DB) any {
			var agents model.IntelligentAgents
			tx.Find(&agents)
			agents.OverrideByRoleTemplate()
			return agents
		})
	})

	c.GetHook(func(c *cosy.Ctx[model.IntelligentAgent]) {
		c.SetPreloads("RoleTemplate.Img")
		c.SetTransformer(func(m *model.IntelligentAgent) any {
			m.GetRole()
			return m
		})
	})

	c.InitRouter(g)
}
