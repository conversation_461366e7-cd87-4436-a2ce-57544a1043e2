package chat

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitRouter(g *gin.RouterGroup) {
	r := g.Group("", api.Guard(acl.IntelligentAgent, acl.Read))
	w := g.Group("", api.Guard(acl.IntelligentAgent, acl.Write))
	r.GET("/chats", GetChatList)
	r.GET("/chats/:id", GetChat)
	w.DELETE("/chats/:id", DeleteChat)
	w.PATCH("/chats/:id", RecoverChat)
	r.GET("/chat_messages", GetChatMessages)
}
