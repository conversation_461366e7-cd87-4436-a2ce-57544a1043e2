package chat

import (
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func GetChatList(c *gin.Context) {
	cosy.Core[model.Chat](c).
		SetEqual("user_id", "agent_id", "device_id").
		SetPreloads("User", "Agent", "Device").
		PagingList()
}

func GetChat(c *gin.Context) {
	cosy.Core[model.Chat](c).
		SetPreloads("User", "Agent", "Device", "Messages").
		Get()
}

func DeleteChat(c *gin.Context) {
	cosy.Core[model.Chat](c).Destroy()
}

func RecoverChat(c *gin.Context) {
	cosy.Core[model.Chat](c).Recover()
}

func GetChatMessages(c *gin.Context) {
	cosy.Core[model.ChatMessage](c).
		SetEqual("user_id", "chat_id", "session_id", "role").
		SetPreloads("Chat").
		PagingList()
}
