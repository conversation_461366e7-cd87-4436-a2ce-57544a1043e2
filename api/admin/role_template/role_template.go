package role_template

import (
	"fmt"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitRoleTemplateRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.RoleTemplate]("role_templates")
	api.CosyGuard(c, acl.RoleTemplate)

	c.CreateHook(func(c *cosy.Ctx[model.RoleTemplate]) {
		c.ExecutedHook(func(c *cosy.Ctx[model.RoleTemplate]) {
			c.Model.PreloadRoleCategories()
		})
	})

	c.ModifyHook(func(c *cosy.Ctx[model.RoleTemplate]) {
		c.ExecutedHook(func(c *cosy.Ctx[model.RoleTemplate]) {
			c.Model.PreloadRoleCategories()
		})
	})

	c.<PERSON>ook(func(c *cosy.Ctx[model.RoleTemplate]) {
		c.ExecutedHook(func(c *cosy.Ctx[model.RoleTemplate]) {
			c.Model.PreloadRoleCategories()
		})
	})

	c.GetListHook(func(c *cosy.Ctx[model.RoleTemplate]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			categoryIds := c.QueryArray("role_category_ids[]")
			if len(categoryIds) > 0 {
				categoryIds = lo.Map(categoryIds, func(id string, _ int) string {
					return fmt.Sprintf("\"%s\"", id)
				})
				tx = tx.Where("JSON_CONTAINS(role_category_ids, ?)", categoryIds)
			}
			return tx
		})

		c.SetScan(func(tx *gorm.DB) any {
			var roleTemplates model.RoleTemplateList
			tx.Find(&roleTemplates)
			roleTemplates.PreloadRoleCategories()
			return roleTemplates
		})
	})

	c.InitRouter(g)
}
