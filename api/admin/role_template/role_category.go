package role_template

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitRoleCategoryRouter(r *gin.RouterGroup) {
	c := cosy.Api[model.RoleCategory]("role_categories")
	api.CosyGuard(c, acl.RoleTemplate)

	c.GetListHook(func(c *cosy.Ctx[model.RoleCategory]) {
		c.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Order("order_id ASC")
		})
	})

	c.InitRouter(r)

	r.POST("role_categories/order",
		api.Guard(acl.RoleTemplate, acl.Write),
		func(c *gin.Context) {
			cosy.Core[model.RoleCategory](c).
				UpdateOrder()
		})
}
