package media

import (
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
	"net/http"
)

func GetMedia(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))
	u := query.Upload
	upload, err := u.Preload(u.User).FirstByID(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.<PERSON>(http.StatusOK, upload)
}

func GetMediaList(c *gin.Context) {
	core := cosy.Core[model.Upload](c).
		SetEqual("user_id", "to").
		SetFussy("name", "mime").
		SetPreloads("User")

	lastId := c.Query("last_id")
	if lastId != "" && lastId != "0" {
		core.GormScope(func(tx *gorm.DB) *gorm.DB {
			return tx.Where("id < ?", cast.ToInt(lastId))
		})
	}

	core.PagingList()
}

func ModifyMedia(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))
	var json struct {
		Name string `json:"name" binding:"required"`
	}

	if !cosy.BindAndValid(c, &json) {
		return
	}

	u := query.Upload

	_, err := u.Where(u.ID.Eq(id)).Updates(&model.Upload{
		Name: json.Name,
	})

	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	GetMedia(c)
}

func DeleteMedia(c *gin.Context) {
	id := cast.ToUint64(c.Param("id"))
	u := query.Upload

	upload, err := u.FirstByID(id)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	if _, err = u.Delete(upload); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
