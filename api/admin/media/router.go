package media

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"github.com/gin-gonic/gin"
)

func InitRouter(r *gin.RouterGroup) {
	r.GET("uploads/:id", api.Guard(acl.Media, acl.Read), GetMedia)
	r.DELETE("uploads/:id", api.Guard(acl.Media, acl.Read, acl.Write), DeleteMedia)
	r.GET("uploads", api.Guard(acl.Media, acl.Read), GetMediaList)
	r.POST("uploads/:id", api.Guard(acl.Media, acl.Read, acl.Write), ModifyMedia)
}
