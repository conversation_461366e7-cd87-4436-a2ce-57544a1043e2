package device

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"gorm.io/gorm"
)

func InitDeviceRouter(r *gin.RouterGroup) {
	c := cosy.Api[model.Device]("devices")
	api.CosyGuard(c, acl.Device)

	// 创建设备的钩子函数
	c.CreateHook(func(c *cosy.Ctx[model.Device]) {
		// 在解码前的钩子，可用于处理一些前置逻辑，例如数据验证、加密等
		// 这里假设没有特殊的前置逻辑
	})

	// 修改设备的钩子函数
	c.ModifyHook(func(c *cosy.Ctx[model.Device]) {
		// 在解码前的钩子，可用于处理一些前置逻辑，例如数据验证、加密等
		// 这里假设没有特殊的前置逻辑
	})

	// 获取设备列表前的钩子函数
	c.BeforeGetList(func(c *gin.Context) {
		// 在获取设备列表前执行的逻辑，例如记录操作日志等
		// 这里假设没有特殊的前置逻辑
	})

	// 获取设备列表的钩子函数
	c.GetListHook(func(ctx *cosy.Ctx[model.Device]) {
		if ctx.Query("bind") == "false" {
			ctx.GormScope(func(tx *gorm.DB) *gorm.DB {
				return tx.Where("user_id = ?", 0)
			})
		} else if ctx.Query("bind") == "true" {
			ctx.GormScope(func(tx *gorm.DB) *gorm.DB {
				return tx.Where("user_id != ?", 0)
			})
		}
	})

	// 删除设备的钩子函数
	c.DestroyHook(func(c *cosy.Ctx[model.Device]) {

	})

	// 初始化路由
	c.InitRouter(r)
	r.POST("/devices/import", api.Guard(acl.Device, acl.Write), ExcelAddDevices)
}
