package device

import (
	"errors"
	"io"
	"net/http"
	"os"

	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// ExcelAddDevices excel 批量导入设备
func ExcelAddDevices(c *gin.Context) {
	// 获取 excel 文件（支持多文件）
	form, err := c.MultipartForm()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	files := form.File["files"]

	successCount := 0
	uniqueMACs := make(map[string]bool)

	d := query.Device

	for _, file := range files {
		func() {
			// 创建临时文件
			tempFile, err := os.CreateTemp("", "device-import-*.xlsx")
			if err != nil {
				logger.Error(err)
				return
			}
			tempFilePath := tempFile.Name()
			defer os.Remove(tempFilePath) // 确保函数结束时删除临时文件

			// 打开上传的文件
			src, err := file.Open()
			if err != nil {
				logger.Error(err)
				return
			}
			defer src.Close()

			// 将上传的文件内容复制到临时文件
			_, err = io.Copy(tempFile, src)
			if err != nil {
				logger.Error(err)
				return
			}

			// 关闭文件，确保内容写入磁盘
			if err = tempFile.Close(); err != nil {
				logger.Error(err)
				return
			}

			// 解析 excel 文件
			f, err := excelize.OpenFile(tempFilePath)
			if err != nil {
				logger.Error(err)
				return
			}

			sheetName := f.GetSheetName(0)
			rows, err := f.GetRows(sheetName)
			if err != nil {
				f.Close()
				logger.Error(err)
				return
			}

			for i, row := range rows {
				if i == 0 {
					// 第 1 行是表头跳过
					continue
				}

				// 第 1 列是 mac 地址
				if row[0] == "" {
					// 如果 mac 地址为空则跳过
					continue
				}

				// 检查是否已处理过该MAC地址
				if uniqueMACs[row[0]] {
					continue
				}

				var device model.Device
				// 查询该 mac 地址是否已存在
				_, err = d.Unscoped().Select(d.MAC).Where(d.MAC.Eq(row[0])).First()
				if err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						device.MAC = row[0]
						if len(row) > 1 {
							device.Identifier = row[1]
						}

						// 添加设备
						err = d.Create(&device)
						if err != nil {
							logger.Error(err)
							continue
						}

						successCount++
						uniqueMACs[row[0]] = true
					} else {
						logger.Error(err)
						continue
					}
				} else {
					// mac 地址已存在则跳过
					uniqueMACs[row[0]] = true
					continue
				}
			}

			f.Close()
		}()
	}

	c.JSON(http.StatusOK, gin.H{
		"code":           http.StatusOK,
		"success_num":    successCount,
		"unique_mac_num": len(uniqueMACs),
	})
}
