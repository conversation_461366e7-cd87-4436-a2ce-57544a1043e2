package analytics

import (
	"net/http"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-api/internal/analytics"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/uozi-tech/cosy"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源的WebSocket连接，生产环境应当限制
	},
}

// 缓存结构体，用于存储最近一次查询的结果
type statsCache struct {
	chatingDeviceCount int
	chatStats          *analytics.ChatMessageStats
	timestamp          int64
	mutex              sync.RWMutex
	lastUpdate         time.Time
}

// 全局缓存实例
var globalStatsCache = &statsCache{
	lastUpdate: time.Time{}, // 初始化为零值，确保第一次调用会更新缓存
}

// 获取或更新缓存数据
func (c *statsCache) getOrUpdate() (int, *analytics.ChatMessageStats, int64, error) {
	c.mutex.RLock()
	// 如果缓存未过期，直接返回缓存数据
	if time.Since(c.lastUpdate) < 5*time.Second {
		defer c.mutex.RUnlock()
		return c.chatingDeviceCount, c.chatStats, c.timestamp, nil
	}
	c.mutex.RUnlock()

	// 缓存已过期，需要更新
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 再次检查，可能在获取写锁的过程中已被其他协程更新
	if time.Since(c.lastUpdate) < 5*time.Second {
		return c.chatingDeviceCount, c.chatStats, c.timestamp, nil
	}

	// 更新缓存
	chatingDeviceCount := analytics.GetChatingDeviceCount()
	chatStats, err := analytics.GetLatestChatMessageStats()
	if err != nil {
		return c.chatingDeviceCount, c.chatStats, time.Now().Unix(), err
	}

	c.chatingDeviceCount = chatingDeviceCount
	c.chatStats = chatStats
	c.timestamp = time.Now().Unix()
	c.lastUpdate = time.Now()

	return c.chatingDeviceCount, c.chatStats, c.timestamp, nil
}

// RealtimeStatsWS WebSocket实时获取统计数据
func RealtimeStatsWS(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	defer conn.Close()

	// 定时发送统计数据
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 从缓存获取或更新数据
			chatingDeviceCount, chatStats, timestamp, err := globalStatsCache.getOrUpdate()

			if err != nil {
				// 如果获取失败，发送错误信息
				if err := conn.WriteJSON(gin.H{
					"chating_device_count": chatingDeviceCount,
					"timestamp":            timestamp,
					"error":                err.Error(),
				}); err != nil {
					return // 如果发送失败，关闭连接
				}
				continue
			}

			// 发送实时统计数据到WebSocket
			if err := conn.WriteJSON(gin.H{
				"chating_device_count": chatingDeviceCount,
				"chat_message_stats":   chatStats,
				"timestamp":            timestamp,
			}); err != nil {
				return // 如果发送失败，关闭连接
			}
		}
	}
}
