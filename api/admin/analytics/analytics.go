package analytics

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"github.com/gin-gonic/gin"
)

// InitRouter 初始化统计分析相关路由
func InitRouter(g *gin.RouterGroup) {
	// 创建analytics路由组
	analyticsGroup := g.Group("/analytics")
	analyticsGroup.Use(api.Guard(acl.ServerAnalytics, acl.Read))

	// 获取所有统计数据（静态数据，包括近14天历史趋势）
	analyticsGroup.GET("", GetAllStatistics)

	// WebSocket实时获取统计数据（包括当前对话设备数量和最新100条消息统计）
	analyticsGroup.GET("/realtime", RealtimeStatsWS)
}
