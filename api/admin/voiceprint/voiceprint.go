package voiceprint

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitVoiceprintRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.Voiceprint]("voiceprints")
	api.CosyGuard(c, acl.IntelligentAgent)

	c.<PERSON>reateHook(func(c *cosy.Ctx[model.Voiceprint]) {
	})

	c.ModifyHook(func(c *cosy.Ctx[model.Voiceprint]) {
	})

	c.BeforeGetList(func(c *gin.Context) {
	})

	c.<PERSON>List<PERSON>ook(func(c *cosy.Ctx[model.Voiceprint]) {
	})

	c.<PERSON><PERSON><PERSON>ook(func(c *cosy.Ctx[model.Voiceprint]) {
	})

	c.InitRouter(g)
}
