package post

import (
	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/internal/post"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitPostRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.Post]("posts")
	api.CosyGuard(c, acl.PostAndPage)

	c.CreateHook(func(c *cosy.Ctx[model.Post]) {
		c.SetValidRules(gin.H{
			"created_at": "omitempty",
		})

		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Post]) {
			if ctx.Model.Type == model.TypePost {
				return
			}

			if ctx.Model.Alias == "" {
				ctx.AbortWithError(post.ErrAliasIsRequired)
				return
			}

			// check if alias is unique
			p := query.Post
			if count, _ := p.Where(p.Alias_.Eq(ctx.Model.Alias)).Count(); count > 0 {
				ctx.AbortWithError(post.ErrAliasShouldBeUnique)
				return
			}
		})
	})

	c.ModifyHook(func(c *cosy.Ctx[model.Post]) {
		c.SetValidRules(gin.H{
			"created_at": "required",
		})

		c.BeforeExecuteHook(func(ctx *cosy.Ctx[model.Post]) {
			if ctx.Model.Type == model.TypePost {
				return
			}

			if ctx.Model.Alias == "" {
				ctx.AbortWithError(post.ErrAliasIsRequired)
				return
			}

			// check if alias is unique
			p := query.Post
			if data, err := p.Where(p.Alias_.Eq(ctx.Model.Alias)).First(); err == nil && data.ID != ctx.OriginModel.ID {
				ctx.AbortWithError(post.ErrAliasShouldBeUnique)
				return
			}
		})
	})

	c.InitRouter(g)
}
