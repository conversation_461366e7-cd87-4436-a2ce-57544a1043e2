package user

import (
	"net/http"

	"git.uozi.org/uozi/potato-api/api"
	"git.uozi.org/uozi/potato-api/internal/acl"
	"git.uozi.org/uozi/potato-api/internal/user"
	"git.uozi.org/uozi/potato-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
)

func InitUserRouter(g *gin.RouterGroup) {
	c := cosy.Api[model.User]("users")
	api.CosyGuard(c, acl.User)

	c.CreateHook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
	})

	c.<PERSON>dify<PERSON>ook(func(c *cosy.Ctx[model.User]) {
		c.BeforeDecodeHook(user.EncryptPassword)
	})

	c.BeforeGetList(func(c *gin.Context) {
		user.PersistLastActive()
	})

	c.<PERSON>ook(func(c *cosy.Ctx[model.User]) {

	})

	c.<PERSON>(func(c *cosy.Ctx[model.User]) {
		c.BeforeExecute<PERSON>ook(func(ctx *cosy.Ctx[model.User]) {
			if ctx.OriginModel.ID == 1 {
				ctx.JSON(http.StatusNotAcceptable, gin.H{
					"message": "Cannot delete the super admin",
				})

				ctx.Abort()
			}
		})

	})

	c.InitRouter(g)

}
